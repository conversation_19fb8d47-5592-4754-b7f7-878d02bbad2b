# 云效流水线自动化部署工具

## 概述

本工具集提供了一套完整的自动化解决方案，用于通过命令行触发云效（Aliyun DevOps）平台的流水线部署。它主要包含一个核心命令和两个辅助脚本，旨在简化开发人员的部署流程，提高效率。

## 核心组件

### 1. 流水线部署命令 (`/pipeline_deployment`)

**文件路径**: `.claude/commands/pipeline_deployment_mcp.md`

这是一个集成在 Claude Code 中的自定义命令，允许用户通过简单的指令触发部署。

#### 使用方法

在终端中执行以下命令：

```bash
/pipeline_deployment <环境> <备注>
```

* **`<环境>`**: 必填参数，指定部署目标环境。目前支持 `dev` (开发环境) 和 `dev2` (测试环境)。如果未指定，默认为 `dev`。
* **`<备注>`**: 可选参数，为本次部署添加备注信息。如果未指定，默认备注为 "Claude Code CLI 自动化部署流水线"。

#### 工作流程

该命令会自动执行以下步骤：
1.  **环境初始化**: 解析用户指令，获取当前 Git 分支。
2.  **获取历史信息**: 查询云效平台，获取上一次部署的运行ID和有效分支列表。
3.  **同步代码**: 将当前分支的本地更改推送到远程仓库。
4.  **执行部署**:
    * 停止上一次可能仍在运行的流水线。
    * 触发新的流水线，部署当前分支及历史有效分支。
5.  **监控结果**: 轮询新流水线的执行状态，直到成功、失败或超时。
6.  **生成报告**: 根据部署结果，生成包含变更详情（新增/移除分支）的总结。
7.  **发送通知**: 如果配置了飞书Webhook，将部署结果发送到指定群聊；否则，在控制台打印结果。

### 2. 状态轮询脚本 (`poll_pipeline_status.sh`)

**文件路径**: `.claude/scripts/poll_pipeline_status.sh`

这是一个独立的 Bash 脚本，负责监控指定流水线实例的运行状态。

#### 使用方法

通常由主命令调用，但也可以独立运行：

```bash
./.claude/scripts/poll_pipeline_status.sh <ORGANIZATION_ID> <PIPELINE_ID> <PIPELINE_RUN_ID> <API_TOKEN>
```

#### 功能特点

* **智能状态检测**: 不仅检查流水线整体状态，还能识别特定的失败原因，如“合并冲突”。
* **详细输出**: 在标准输出（stdout）中返回简洁的原因字符串（如 "Deployment Succeeded" 或 "Merge Conflict in Stage [分支集成]"）。
* **进度反馈**: 在标准错误（stderr）中输出轮询进度。
* **超时机制**: 默认超时时间为15分钟（900秒）。
* **退出码**:
    * `0`: 部署成功。
    * `2`: 轮询超时。
    * `3`: 检测到合并冲突。
    * `4`: 流水线阶段失败或被取消。
    * `5`: 参数错误。

### 3. 环境设置脚本 (`pipeline_deployment_setup.sh`)

**文件路径**: `.claude/scripts/pipeline_deployment_setup.sh`

这是一个用于初始化和配置部署环境的脚本，确保所有前置条件都已满足。

#### 使用方法

在首次使用或环境变量变更后，运行此脚本：

```bash
./.claude/scripts/pipeline_deployment_setup.sh
```

#### 执行步骤

1.  **验证API Token**: 检查 `ALIYUN_API_TOKEN` 环境变量是否已设置并有效。
2.  **配置MCP服务器**: 自动为 Claude Code 配置云效 MCP (Model Context Protocol) 服务器，以便后续命令可以直接调用云效API。
3.  **检查流水线ID配置**: 读取 `.claude/commands/pipeline_deployment_mcp.md` 文件，检查 `dev` 和 `dev2` 环境的流水线ID是否已配置。
4.  **辅助配置**:
    * 自动从当前 Git 仓库获取代码库名称。
    * 查询并列出云效平台上与该代码库相关的所有流水线，方便用户查找正确的流水线ID。
    * 提供清晰的配置指南，指导用户如何在配置文件中填入正确的流水线ID。

## 快速开始

1.  **配置环境变量**:
    在你的 shell 配置文件（如 `~/.bashrc` 或 `~/.zshrc`）中添加：
    ```bash
    export ALIYUN_API_TOKEN="your_aliyun_api_token_here"
    export FEISHU_WEBHOOK="your_feishu_webhook_url_here" # 可选，用于接收部署通知
    ```
    然后执行 `source ~/.bashrc` 使配置生效。

2.  **运行设置脚本**:
    ```bash
    ./.claude/scripts/pipeline_deployment_setup.sh
    ```
    根据脚本的输出，找到你需要的流水线ID，并将其填入 `.claude/commands/pipeline_deployment_mcp.md` 文件的对应位置。

3.  **触发部署**:
    在你的项目根目录下，执行部署命令：
    ```bash
    /pipeline_deployment dev2 "部署新功能X"
    ```
    脚本将自动完成剩余的所有工作，并通知你结果。

## 注意事项

* 请确保你拥有对应云效流水线的执行权限。
* `ALIYUN_API_TOKEN` 是访问云效OpenAPI的密钥，请妥善保管。
* 获取方式：https://account-devops.aliyun.com/settings/personalAccessToken
![img.png](imgs/阿里云访问TOKEN获取.png)
* 飞书通知是可选功能，新建一个只有自己的群聊，添加机器人。
将WEBHOOK地址配置到环境变量，例如FEISHU_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/xxx"。如果未配置 `FEISHU_WEBHOOK`，结果将直接在终端输出。
![img_1.png](imgs/飞书WEBHOOK配置.png)
