# 合并请求代码审查工具安装脚本 (PowerShell版本)
# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== 合并请求代码审查工具安装脚本 ===" -ForegroundColor Cyan
Write-Host ""

# 检查Python版本
Write-Host "正在检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 检测到Python: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python未找到"
    }
} catch {
    Write-Host "✗ 未检测到Python，请先安装Python" -ForegroundColor Red
    Write-Host ""
    Write-Host "请访问 https://www.python.org/downloads/ 下载并安装Python" -ForegroundColor Yellow
    Write-Host "安装时请确保勾选 'Add Python to PATH' 选项" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查pip
Write-Host "正在检查pip..." -ForegroundColor Yellow
try {
    $pipVersion = pip --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 检测到pip: $pipVersion" -ForegroundColor Green
    } else {
        throw "pip未找到"
    }
} catch {
    Write-Host "✗ 未检测到pip，请确保Python安装正确" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 安装依赖
Write-Host ""
Write-Host "正在安装Python依赖包..." -ForegroundColor Yellow
try {
    pip install -r requirements.txt
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 依赖包安装成功" -ForegroundColor Green
    } else {
        throw "依赖包安装失败"
    }
} catch {
    Write-Host "✗ 依赖包安装失败" -ForegroundColor Red
    Write-Host "请检查网络连接或尝试使用国内镜像源：" -ForegroundColor Yellow
    Write-Host "pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查环境变量
Write-Host ""
Write-Host "检查环境变量配置..." -ForegroundColor Yellow

# 检查阿里云访问密钥
$alibabaKeyMissing = $false
$accessKeyId = [Environment]::GetEnvironmentVariable("ALIBABA_CLOUD_ACCESS_KEY_ID")
$accessKeySecret = [Environment]::GetEnvironmentVariable("ALIBABA_CLOUD_ACCESS_KEY_SECRET")

if ([string]::IsNullOrEmpty($accessKeyId)) {
    Write-Host "⚠ 未设置 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量" -ForegroundColor Yellow
    $alibabaKeyMissing = $true
} else {
    Write-Host "✓ ALIBABA_CLOUD_ACCESS_KEY_ID 已设置" -ForegroundColor Green
}

if ([string]::IsNullOrEmpty($accessKeySecret)) {
    Write-Host "⚠ 未设置 ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量" -ForegroundColor Yellow
    $alibabaKeyMissing = $true
} else {
    Write-Host "✓ ALIBABA_CLOUD_ACCESS_KEY_SECRET 已设置" -ForegroundColor Green
}

# 如果阿里云密钥缺失，提示用户输入
if ($alibabaKeyMissing) {
    Write-Host ""
    Write-Host "=== 配置阿里云访问密钥 ===" -ForegroundColor Cyan
    Write-Host "为了使用云效API，需要配置阿里云访问密钥。" -ForegroundColor Yellow
    Write-Host "您可以在阿里云控制台 -> AccessKey管理 中获取这些信息。" -ForegroundColor Yellow
    Write-Host ""
    
    # 获取用户输入
    if ([string]::IsNullOrEmpty($accessKeyId)) {
        $accessKeyId = Read-Host "请输入 ALIBABA_CLOUD_ACCESS_KEY_ID"
        if ([string]::IsNullOrEmpty($accessKeyId)) {
            Write-Host "✗ ACCESS_KEY_ID 不能为空" -ForegroundColor Red
            Read-Host "按任意键退出"
            exit 1
        }
    }
    
    if ([string]::IsNullOrEmpty($accessKeySecret)) {
        $accessKeySecret = Read-Host "请输入 ALIBABA_CLOUD_ACCESS_KEY_SECRET" -AsSecureString
        $accessKeySecret = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($accessKeySecret))
        if ([string]::IsNullOrEmpty($accessKeySecret)) {
            Write-Host "✗ ACCESS_KEY_SECRET 不能为空" -ForegroundColor Red
            Read-Host "按任意键退出"
            exit 1
        }
    }
    
    # 设置用户环境变量
    Write-Host ""
    Write-Host "正在设置阿里云访问密钥到用户环境变量..." -ForegroundColor Yellow
    
    try {
        [Environment]::SetEnvironmentVariable("ALIBABA_CLOUD_ACCESS_KEY_ID", $accessKeyId, "User")
        Write-Host "✓ ALIBABA_CLOUD_ACCESS_KEY_ID 设置成功" -ForegroundColor Green
        
        [Environment]::SetEnvironmentVariable("ALIBABA_CLOUD_ACCESS_KEY_SECRET", $accessKeySecret, "User")
        Write-Host "✓ ALIBABA_CLOUD_ACCESS_KEY_SECRET 设置成功" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "✓ 阿里云访问密钥已设置到用户环境变量" -ForegroundColor Green
        Write-Host "请重启PowerShell或命令提示符使环境变量生效。" -ForegroundColor Yellow
    } catch {
        Write-Host "✗ 设置环境变量时发生错误: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
}

# 检查LLM API密钥
$llmKeyMissing = $false
$llmApiKey = [Environment]::GetEnvironmentVariable("XM_LLM_API_KEY")

if ([string]::IsNullOrEmpty($llmApiKey)) {
    Write-Host "⚠ 未设置 XM_LLM_API_KEY 环境变量" -ForegroundColor Yellow
    $llmKeyMissing = $true
} else {
    Write-Host "✓ XM_LLM_API_KEY 已设置" -ForegroundColor Green
}

# 如果LLM API密钥缺失，提示用户输入
if ($llmKeyMissing) {
    Write-Host ""
    Write-Host "=== 配置LLM API密钥 ===" -ForegroundColor Cyan
    Write-Host "为了使用AI代码审查功能，需要配置LLM API密钥。" -ForegroundColor Yellow
    Write-Host "您可以访问 https://litellm-test.summerfarm.net/v1 获取API_KEY。" -ForegroundColor Yellow
    Write-Host ""
    
    $llmApiKey = Read-Host "请输入 XM_LLM_API_KEY"
    if ([string]::IsNullOrEmpty($llmApiKey)) {
        Write-Host "✗ XM_LLM_API_KEY 不能为空" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    
    # 设置用户环境变量
    Write-Host ""
    Write-Host "正在设置LLM API密钥到用户环境变量..." -ForegroundColor Yellow
    
    try {
        [Environment]::SetEnvironmentVariable("XM_LLM_API_KEY", $llmApiKey, "User")
        Write-Host "✓ XM_LLM_API_KEY 设置成功" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "✓ LLM API密钥已设置到用户环境变量" -ForegroundColor Green
        Write-Host "请重启PowerShell或命令提示符使环境变量生效。" -ForegroundColor Yellow
    } catch {
        Write-Host "✗ 设置环境变量时发生错误: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
}

# 检查其他可选环境变量
$envVars = @(
    @{Name="XM_LLM_BASE_URL"; Description="API基础URL"},
    @{Name="XM_LLM_MODEL"; Description="模型名称"}
)

foreach ($envVar in $envVars) {
    $value = [Environment]::GetEnvironmentVariable($envVar.Name)
    if ([string]::IsNullOrEmpty($value)) {
        Write-Host "⚠ 未设置 $($envVar.Name) 环境变量，将使用默认值" -ForegroundColor Yellow
    } else {
        Write-Host "✓ $($envVar.Name) 已设置" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "=== 安装完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "使用方法：" -ForegroundColor Cyan
Write-Host "  python mr_code_review.py" -ForegroundColor White
Write-Host ""
Write-Host "示例URL：" -ForegroundColor Cyan
Write-Host "  https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs" -ForegroundColor White
Write-Host ""
Write-Host "环境变量配置：" -ForegroundColor Cyan
Write-Host "  阿里云访问密钥（必需）：" -ForegroundColor Yellow
Write-Host "    临时设置：" -ForegroundColor Gray
Write-Host "      `$env:ALIBABA_CLOUD_ACCESS_KEY_ID = `"your-access-key-id`"" -ForegroundColor White
Write-Host "      `$env:ALIBABA_CLOUD_ACCESS_KEY_SECRET = `"your-access-key-secret`"" -ForegroundColor White
Write-Host "    永久设置：" -ForegroundColor Gray
Write-Host "      [Environment]::SetEnvironmentVariable(`"ALIBABA_CLOUD_ACCESS_KEY_ID`", `"your-access-key-id`", `"User`")" -ForegroundColor White
Write-Host "      [Environment]::SetEnvironmentVariable(`"ALIBABA_CLOUD_ACCESS_KEY_SECRET`", `"your-access-key-secret`", `"User`")" -ForegroundColor White
Write-Host ""
Write-Host "  LLM API密钥（必需）：" -ForegroundColor Yellow
Write-Host "    临时设置：" -ForegroundColor Gray
Write-Host "      `$env:XM_LLM_API_KEY = `"your-api-key`"" -ForegroundColor White
Write-Host "    永久设置：" -ForegroundColor Gray
Write-Host "      [Environment]::SetEnvironmentVariable(`"XM_LLM_API_KEY`", `"your-api-key`", `"User`")" -ForegroundColor White
Write-Host ""
Write-Host "  LLM配置（可选）：" -ForegroundColor Yellow
Write-Host "    临时设置：" -ForegroundColor Gray
Write-Host "      `$env:XM_LLM_BASE_URL = `"https://litellm-test.summerfarm.net/v1`"" -ForegroundColor White
Write-Host "      `$env:XM_LLM_MODEL = `"deepseek-v3-250324`"" -ForegroundColor White
Write-Host "    永久设置：" -ForegroundColor Gray
Write-Host "      [Environment]::SetEnvironmentVariable(`"XM_LLM_BASE_URL`", `"https://litellm-test.summerfarm.net/v1`", `"User`")" -ForegroundColor White
Write-Host "      [Environment]::SetEnvironmentVariable(`"XM_LLM_MODEL`", `"deepseek-v3-250324`", `"User`")" -ForegroundColor White
Write-Host ""
Write-Host "或者通过系统设置 -> 高级系统设置 -> 环境变量 进行配置" -ForegroundColor Yellow
Write-Host ""
Read-Host "按任意键退出"
