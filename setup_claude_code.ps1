# Claude Code 安装和配置脚本 (PowerShell 版本)
# 用于安装 Claude Code 和 Claude Code Router，并配置相关文件

Write-Host "🚀 开始安装 Claude Code 和 Claude Code Router..."

# 检查 Node.js 和 npm 是否已安装
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 错误: npm 未安装，请先安装 Node.js 和 npm" -ForegroundColor Red
    exit 1
}

Write-Host "📦 正在安装 Claude Code..."
# 安装 Claude Code
npm install -g @anthropic-ai/claude-code --registry=https://registry.npmmirror.com

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Claude Code 安装成功" -ForegroundColor Green
} else {
    Write-Host "❌ Claude Code 安装失败" -ForegroundColor Red
    exit 1
}

Write-Host "📦 正在安装 Claude Code Router..."
# 安装 Claude Code Router
npm install -g @musistudio/claude-code-router --registry=https://registry.npmmirror.com

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Claude Code Router 安装成功" -ForegroundColor Green
} else {
    Write-Host "❌ Claude Code Router 安装失败" -ForegroundColor Red
    exit 1
}

Write-Host "📁 正在配置 Claude Code 命令..."

# 创建 $env:USERPROFILE\.claude\commands 目录
$CLAUDE_COMMANDS_DIR = "$env:USERPROFILE\.claude\commands"
if (-not (Test-Path $CLAUDE_COMMANDS_DIR)) {
    New-Item -ItemType Directory -Path $CLAUDE_COMMANDS_DIR | Out-Null
}

# 复制 xm 文件夹到用户的 $env:USERPROFILE\.claude\commands 目录
if (Test-Path "claude\commands\xm") {
    Copy-Item -Path "claude\commands\xm" -Destination "$CLAUDE_COMMANDS_DIR\xm" -Recurse -Force
    Write-Host "✅ 已复制 xm 命令到 $CLAUDE_COMMANDS_DIR\xm" -ForegroundColor Green
} else {
    Write-Host "⚠️  警告: claude\commands\xm 目录不存在，跳过复制" -ForegroundColor Yellow
}

Write-Host "📁 正在配置 Claude Code Router..."

# 创建 $env:USERPROFILE\.claude-code-router 目录
$CLAUDE_ROUTER_DIR = "$env:USERPROFILE\.claude-code-router"
if (-not (Test-Path $CLAUDE_ROUTER_DIR)) {
    New-Item -ItemType Directory -Path $CLAUDE_ROUTER_DIR | Out-Null
}

# 复制 config.json 到 $env:USERPROFILE\.claude-code-router\config.json
if (Test-Path "claude\claude-code-router\config.json") {
    Copy-Item -Path "claude\claude-code-router\config.json" -Destination "$CLAUDE_ROUTER_DIR\config.json" -Force
    Write-Host "✅ 已复制配置文件到 $CLAUDE_ROUTER_DIR\config.json" -ForegroundColor Green
} else {
    Write-Host "⚠️  警告: claude\claude-code-router\config.json 文件不存在，跳过复制" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Claude Code 和 Claude Code Router 安装配置完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 安装的组件："
Write-Host "   • Claude Code (全局安装)"
Write-Host "   • Claude Code Router (全局安装)"
Write-Host "   • xm 命令集 (位于 $env:USERPROFILE\.claude\commands\xm)"
Write-Host "   • Router 配置文件 (位于 $env:USERPROFILE\.claude-code-router\config.json)"
Write-Host ""
Write-Host "🔧 使用方法："
Write-Host "   • 运行 'claude-code' 启动 Claude Code"
Write-Host "   • 运行 'claude-code-router' 启动 Claude Code Router"
Write-Host ""
Write-Host "💡 提示："
Write-Host "   • 如需修改配置，请编辑 $env:USERPROFILE\.claude-code-router\config.json"
Write-Host "   • 如需添加更多命令，请在 $env:USERPROFILE\.claude\commands 目录下添加"
Write-Host ""