# Windows 平台安装指南

本项目提供了两种Windows平台的安装脚本，请根据你的系统环境选择合适的方式。

## 方式一：使用批处理脚本 (推荐新手)

### 运行方式
1. 双击 `setup.bat` 文件
2. 或在命令提示符中运行：
   ```cmd
   setup.bat
   ```

### 特点
- 兼容性好，适用于所有Windows版本
- 界面简单，操作直观
- 自动检查Python和pip环境
- 自动安装项目依赖

## 方式二：使用PowerShell脚本 (推荐高级用户)

### 运行方式
1. 右键点击 `setup.ps1` 文件，选择"使用PowerShell运行"
2. 或在PowerShell中运行：
   ```powershell
   .\setup.ps1
   ```

### 特点
- 更好的错误处理和用户体验
- 彩色输出，界面更友好
- 提供详细的环境变量配置说明
- 支持更高级的功能

### 执行策略问题
如果遇到"无法加载文件，因为在此系统上禁止运行脚本"的错误，请：

1. 以管理员身份打开PowerShell
2. 运行以下命令临时允许脚本执行：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```
3. 运行完成后可以恢复原设置：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy Restricted -Scope CurrentUser
   ```

## 前置要求

### Python 环境
- Python 3.7 或更高版本
- 安装时请确保勾选 "Add Python to PATH" 选项

### 下载地址
- 官方下载：https://www.python.org/downloads/
- 国内镜像：https://npm.taobao.org/mirrors/python/

## 环境变量配置

安装完成后，你可以配置以下环境变量（可选）：

### 临时设置（当前会话有效）
```cmd
# 命令提示符
set XM_LLM_API_KEY=your-api-key
set XM_LLM_BASE_URL=https://litellm-test.summerfarm.net/v1
set XM_LLM_MODEL=deepseek-v3-250324
```

```powershell
# PowerShell
$env:XM_LLM_API_KEY = "your-api-key"
$env:XM_LLM_BASE_URL = "https://litellm-test.summerfarm.net/v1"
$env:XM_LLM_MODEL = "deepseek-v3-250324"
```

### 永久设置
1. **通过系统设置**：
   - 右键"此电脑" → 属性 → 高级系统设置 → 环境变量
   - 在"用户变量"中添加上述变量

2. **通过PowerShell**：
   ```powershell
   [Environment]::SetEnvironmentVariable("XM_LLM_API_KEY", "your-api-key", "User")
   [Environment]::SetEnvironmentVariable("XM_LLM_BASE_URL", "https://litellm-test.summerfarm.net/v1", "User")
   [Environment]::SetEnvironmentVariable("XM_LLM_MODEL", "deepseek-v3-250324", "User")
   ```

## 使用方法

安装完成后，在项目目录中运行：

```cmd
python mr_code_review.py
```

然后输入合并请求的URL即可开始代码审查。

## 常见问题

### 1. Python未找到
- 确保已安装Python并添加到PATH
- 重启命令提示符或PowerShell
- 尝试使用 `py` 命令代替 `python`

### 2. pip安装失败
- 检查网络连接
- 使用国内镜像源：
  ```cmd
  pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
  ```

### 3. 权限问题
- 以管理员身份运行脚本
- 检查防火墙和杀毒软件设置

## 技术支持

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 网络连接是否正常
3. 防火墙设置是否阻止了安装
4. 是否有足够的磁盘空间
