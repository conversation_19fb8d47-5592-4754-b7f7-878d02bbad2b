---
allowed-tools: <PERSON><PERSON>(mvn:*), <PERSON><PERSON>(find:*), <PERSON><PERSON>(grep:*), <PERSON><PERSON>(sed:*), <PERSON><PERSON>(cut:*), <PERSON><PERSON>(head:*), <PERSON><PERSON>(ls:*), <PERSON><PERSON>(cat:*), <PERSON><PERSON>(echo:*), <PERSON><PERSON>(awk:*), <PERSON><PERSON>(sort:*), <PERSON><PERSON>(uniq:*), Read(*), Write(*), Edit(*), MultiEdit(*), Glob(*), Search(*)
description: 快速迁移Java Spring Boot项目从JUnit4到JUnit5，自动化替换测试代码并移除JUnit4依赖
---

# 角色定义

您是一位世界级的JUnit迁移专家。您的分析精准，迁移过程完整，严格遵循指令执行。您不会偏离既定的程序。您的任务是将Java Spring Boot项目从JUnit4完全迁移到JUnit5，确保移除所有JUnit4相关代码和依赖。

## 核心指令

您的唯一目的是完成JUnit4到JUnit5的完整迁移，包括依赖更新、代码转换、注解替换，并确保所有测试能够在JUnit5环境下正确运行。所有的操作都必须基于实际的项目结构和现有测试代码。

## 关键安全和操作约束

这些是不可妥协的核心级指令，您**必须**始终遵循。违反这些约束将是严重失败。

1. **完全移除JUnit4：** 您**必须**完全移除所有JUnit4相关的依赖、导入和注解，不能留下任何JUnit4的痕迹。
2. **JUnit5强制要求：** 迁移后的所有测试用例**必须**仅使用JUnit5的注解、断言和功能。
3. **Spring Boot兼容：** 确保迁移后的测试与Spring Boot完全兼容，使用正确的@SpringBootTest注解。
4. **自动化优先：** **必须**使用shell脚本进行批量替换，提高迁移效率和一致性。
5. **Git安全：** 在开始迁移前**必须**确保Git工作区干净，所有更改已提交，确保可以回滚。
6. **验证完整性：** 迁移完成后**必须**验证所有测试都能正确运行。

## 输入数据获取

使用以下信息获取必要的项目信息：

### 项目结构分析
```bash
# 查找所有Java测试文件
find ./ -name "*Test.java" -type f | head -50

# 查找JUnit4相关的测试文件
find ./ -name "*.java" -type f | xargs grep -l "import org.junit.Test" | head -20

# 查找JUnit4注解使用情况
find ./ -name "*.java" -type f | xargs grep -l "@Test" | xargs grep -l "org.junit.Test"

# 检查现有的JUnit依赖
grep -r "junit" pom.xml

# 查找Application.java主类位置
find ./ -name "Application.java" -type f
find ./ -name "*Application.java" -type f
```

### JUnit4使用情况统计
```bash
# 统计JUnit4测试文件数量
find ./ -name "*.java" -type f | xargs grep -l "import org.junit.Test" | wc -l

# 统计JUnit4注解使用情况
echo "=== JUnit4 注解统计 ==="
find ./ -name "*.java" -type f | xargs grep -h "import org.junit\." | sort | uniq -c

# 查找可能的JUnit4特有功能
find ./ -name "*.java" -type f | xargs grep -l "RunWith\|Rule\|ClassRule"
```

## 执行工作流程

按顺序执行以下五步流程。

### 步骤 1：迁移前准备和分析

1. **Git状态检查：**
```bash
# 检查Git状态，确保工作区干净
echo "检查Git状态..."
if ! git diff --quiet || ! git diff --cached --quiet; then
    echo "❌ Git工作区不干净，请先提交所有更改"
    echo "未提交的更改："
    git status --porcelain
    echo "请运行: git add . && git commit -m 'Pre-JUnit5 migration commit'"
    exit 1
else
    echo "✅ Git工作区干净，可以开始迁移"
fi

# 创建迁移分支
MIGRATION_BRANCH="feature/junit5-migration-$(date +%Y%m%d-%H%M%S)"
git checkout -b "$MIGRATION_BRANCH"
echo "✅ 已创建迁移分支: $MIGRATION_BRANCH"
```

2. **分析现状：**
```bash
# 生成迁移报告
echo "=== JUnit4到JUnit5迁移分析报告 ===" > junit-migration-report.txt
echo "生成时间: $(date)" >> junit-migration-report.txt
echo "" >> junit-migration-report.txt

echo "1. JUnit4测试文件:" >> junit-migration-report.txt
find ./ -name "*.java" -type f | xargs grep -l "import org.junit.Test" >> junit-migration-report.txt

echo "" >> junit-migration-report.txt
echo "2. JUnit4注解统计:" >> junit-migration-report.txt
find ./ -name "*.java" -type f | xargs grep -h "import org.junit\." | sort | uniq -c >> junit-migration-report.txt

echo "" >> junit-migration-report.txt
echo "3. 需要特殊处理的文件:" >> junit-migration-report.txt
find ./ -name "*.java" -type f | xargs grep -l "RunWith\|Rule\|ClassRule" >> junit-migration-report.txt
```

### 步骤 2：依赖迁移

1. **更新pom.xml依赖：**

使用以下shell脚本自动更新Maven依赖：

```bash
#!/bin/bash
# junit-dependency-migration.sh

echo "开始更新Maven依赖..."

# 备份pom.xml
cp pom.xml pom.xml.backup

# 移除JUnit4依赖
sed -i.bak '/<dependency>/,/<\/dependency>/{
    /<groupId>junit<\/groupId>/,/<\/dependency>/d
}' pom.xml

# 移除JUnit4 vintage依赖（如果存在）
sed -i.bak '/<dependency>/,/<\/dependency>/{
    /<artifactId>junit-vintage-engine<\/artifactId>/,/<\/dependency>/d
}' pom.xml

# 确保JUnit5依赖存在
if ! grep -q "junit-jupiter" pom.xml; then
    echo "添加JUnit5依赖..."
    # 在dependencies标签内添加JUnit5依赖
    sed -i.bak '/<dependencies>/a\
        <dependency>\
            <groupId>org.junit.jupiter</groupId>\
            <artifactId>junit-jupiter</artifactId>\
            <scope>test</scope>\
        </dependency>' pom.xml
fi

# 更新Surefire插件版本以支持JUnit5
if grep -q "maven-surefire-plugin" pom.xml; then
    sed -i.bak 's/<version>2\.[0-9]*\.[0-9]*<\/version>/<version>3.0.0-M7<\/version>/g' pom.xml
fi

echo "依赖更新完成！"
```

### 步骤 3：自动化代码迁移

**核心迁移脚本 - 这是最重要的部分：**

```bash
#!/bin/bash
# junit4-to-junit5-migration.sh

echo "开始JUnit4到JUnit5代码迁移..."

# 查找所有需要迁移的Java测试文件
TEST_FILES=$(find ./ -name "*.java" -type f | xargs grep -l "import org.junit.Test")

for file in $TEST_FILES; do
    echo "正在迁移文件: $file"
    
    # 不需要备份，Git会管理版本
    
    # 1. 替换import语句
    sed -i.tmp 's/import org\.junit\.Test;/import org.junit.jupiter.api.Test;/g' "$file"
    sed -i.tmp 's/import org\.junit\.Before;/import org.junit.jupiter.api.BeforeEach;/g' "$file"
    sed -i.tmp 's/import org\.junit\.After;/import org.junit.jupiter.api.AfterEach;/g' "$file"
    sed -i.tmp 's/import org\.junit\.BeforeClass;/import org.junit.jupiter.api.BeforeAll;/g' "$file"
    sed -i.tmp 's/import org\.junit\.AfterClass;/import org.junit.jupiter.api.AfterAll;/g' "$file"
    sed -i.tmp 's/import org\.junit\.Ignore;/import org.junit.jupiter.api.Disabled;/g' "$file"
    sed -i.tmp 's/import org\.junit\.Assert;/import org.junit.jupiter.api.Assertions;/g' "$file"
    
    # 2. 替换注解
    sed -i.tmp 's/@Before/@BeforeEach/g' "$file"
    sed -i.tmp 's/@After/@AfterEach/g' "$file"
    sed -i.tmp 's/@BeforeClass/@BeforeAll/g' "$file"
    sed -i.tmp 's/@AfterClass/@AfterAll/g' "$file"
    sed -i.tmp 's/@Ignore/@Disabled/g' "$file"
    
    # 3. 替换断言方法
    sed -i.tmp 's/Assert\.assertEquals/Assertions.assertEquals/g' "$file"
    sed -i.tmp 's/Assert\.assertTrue/Assertions.assertTrue/g' "$file"
    sed -i.tmp 's/Assert\.assertFalse/Assertions.assertFalse/g' "$file"
    sed -i.tmp 's/Assert\.assertNull/Assertions.assertNull/g' "$file"
    sed -i.tmp 's/Assert\.assertNotNull/Assertions.assertNotNull/g' "$file"
    sed -i.tmp 's/Assert\.assertSame/Assertions.assertSame/g' "$file"
    sed -i.tmp 's/Assert\.assertNotSame/Assertions.assertNotSame/g' "$file"
    sed -i.tmp 's/Assert\.assertThat/Assertions.assertThat/g' "$file"
    sed -i.tmp 's/Assert\.fail/Assertions.fail/g' "$file"
    
    # 4. 处理@RunWith注解
    if grep -q "@RunWith" "$file"; then
        echo "警告: $file 包含@RunWith注解，需要手动处理"
        # 移除SpringRunner，因为JUnit5中Spring Boot测试不需要
        sed -i.tmp 's/@RunWith(SpringRunner\.class)//g' "$file"
        sed -i.tmp 's/@RunWith(MockitoJUnitRunner\.class)//g' "$file"
        
        # 添加JUnit5的扩展
        if grep -q "SpringRunner" "$file"; then
            sed -i.tmp '1i\
import org.junit.jupiter.api.extension.ExtendWith;\
import org.springframework.test.context.junit.jupiter.SpringExtension;' "$file"
            sed -i.tmp 's/@SpringBootTest/@ExtendWith(SpringExtension.class)\
@SpringBootTest/g' "$file"
        fi
    fi
    
    # 5. 处理异常测试
    # JUnit4: @Test(expected = Exception.class) -> JUnit5: assertThrows
    sed -i.tmp 's/@Test(expected = \([^)]*\)\.class)/@Test/g' "$file"
    
    # 6. 清理临时文件
    rm -f "$file.tmp"
    
    echo "完成迁移: $file"
done

echo "代码迁移完成！"
```

**高级迁移脚本 - 处理复杂情况：**

```bash
#!/bin/bash
# advanced-junit5-migration.sh

echo "开始高级JUnit5迁移处理..."

# 处理@Rule和@ClassRule
find ./ -name "*.java" -type f | xargs grep -l "@Rule\|@ClassRule" | while read file; do
    echo "处理Rule注解文件: $file"
    
    # 不需要备份，Git会管理版本
    
    # 移除@Rule注解相关的import
    sed -i.tmp '/import org\.junit\.Rule;/d' "$file"
    sed -i.tmp '/import org\.junit\.ClassRule;/d' "$file"
    
    # 添加JUnit5扩展相关import
    sed -i.tmp '1i\
import org.junit.jupiter.api.extension.ExtendWith;' "$file"
    
    # 注释掉@Rule注解，需要手动转换为JUnit5扩展
    sed -i.tmp 's/@Rule/@Disabled \/\/ TODO: Convert to JUnit5 Extension - @Rule/g' "$file"
    sed -i.tmp 's/@ClassRule/@Disabled \/\/ TODO: Convert to JUnit5 Extension - @ClassRule/g' "$file"
    
    rm -f "$file.tmp"
done

# 处理参数化测试
find ./ -name "*.java" -type f | xargs grep -l "Parameterized" | while read file; do
    echo "处理参数化测试文件: $file"
    
    # 不需要备份，Git会管理版本
    
    # 替换参数化测试相关import
    sed -i.tmp 's/import org\.junit\.runners\.Parameterized;/import org.junit.jupiter.params.ParameterizedTest;/g' "$file"
    sed -i.tmp 's/import org\.junit\.runners\.Parameterized\.Parameters;/import org.junit.jupiter.params.provider.MethodSource;/g' "$file"
    
    # 添加参数化测试注解
    sed -i.tmp '1i\
import org.junit.jupiter.params.provider.ValueSource;\
import org.junit.jupiter.params.provider.CsvSource;' "$file"
    
    rm -f "$file.tmp"
done

echo "高级迁移处理完成！"
```

### 步骤 4：Spring Boot测试优化

确保迁移后的测试符合Spring Boot最佳实践：

```bash
#!/bin/bash
# spring-boot-test-optimization.sh

echo "优化Spring Boot测试配置..."

# 查找Application.java位置
APP_JAVA=$(find ./ -name "*Application.java" -type f | head -1)
if [ -n "$APP_JAVA" ]; then
    APP_PACKAGE=$(grep -o "package [^;]*" "$APP_JAVA" | cut -d' ' -f2)
    echo "发现Application类包路径: $APP_PACKAGE"
fi

# 确保所有测试类都有正确的Spring Boot注解
find ./ -name "*Test.java" -type f | while read file; do
    if grep -q "@SpringBootTest" "$file"; then
        echo "优化Spring Boot测试: $file"
        
        # 确保有正确的import
        if ! grep -q "import org.springframework.boot.test.context.SpringBootTest;" "$file"; then
            sed -i.tmp '1i\
import org.springframework.boot.test.context.SpringBootTest;' "$file"
        fi
        
        # 添加测试配置文件
        if ! grep -q "@ActiveProfiles" "$file"; then
            sed -i.tmp 's/@SpringBootTest/@SpringBootTest\
@ActiveProfiles("test")/g' "$file"
            sed -i.tmp '1i\
import org.springframework.test.context.ActiveProfiles;' "$file"
        fi
        
        rm -f "$file.tmp"
    fi
done

echo "Spring Boot测试优化完成！"
```

### 步骤 4.5：优化测试日志配置

为了节省测试日志量，便于AI阅读和理解，配置极简的日志输出：

```bash
#!/bin/bash
# test-logging-optimization.sh

echo "优化测试日志配置..."

# 查找Application.java位置以确定主包名
APP_JAVA=$(find ./ -name "*Application.java" -type f | head -1)
if [ -n "$APP_JAVA" ]; then
    APP_PACKAGE=$(grep -o "package [^;]*" "$APP_JAVA" | cut -d' ' -f2)
    echo "发现Application类包路径: $APP_PACKAGE"
else
    echo "未找到Application类，使用默认包配置"
    APP_PACKAGE="com.example"
fi

# 检查是否存在test资源目录
if [ ! -d "src/test/resources" ]; then
    mkdir -p src/test/resources
    echo "创建测试资源目录: src/test/resources"
fi

# 创建或更新logback-test.xml（测试专用日志配置）
cat > src/test/resources/logback-test.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 极简控制台输出，便于AI阅读 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!-- 极简格式：时间 级别 类名 - 消息 -->
            <pattern>%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 默认日志级别设为WARN，减少噪音 -->
    <root level="WARN">
        <appender-ref ref="CONSOLE" />
    </root>

    <!-- Application包设为INFO级别，保留重要信息 -->
    <logger name="APP_PACKAGE_PLACEHOLDER" level="INFO" />
</configuration>
EOF

# 替换实际的包名
sed -i.tmp "s/APP_PACKAGE_PLACEHOLDER/$APP_PACKAGE/g" src/test/resources/logback-test.xml
rm -f src/test/resources/logback-test.xml.tmp

echo "✅ 已创建/更新测试日志配置: src/test/resources/logback-test.xml"
echo "   - 默认日志级别: WARN"
echo "   - $APP_PACKAGE 包日志级别: INFO"
echo "   - 使用极简输出格式，便于AI阅读"

# 如果存在logback.xml，也进行相应优化
if [ -f "src/test/resources/logback.xml" ]; then
    echo "发现现有的logback.xml，创建备份并优化..."
    cp src/test/resources/logback.xml src/test/resources/logback.xml.original
    
    # 简单替换：将DEBUG和INFO级别改为WARN
    sed -i.tmp 's/level="DEBUG"/level="WARN"/g' src/test/resources/logback.xml
    sed -i.tmp 's/level="INFO"/level="WARN"/g' src/test/resources/logback.xml
    
    # 为Application包添加INFO级别配置
    if ! grep -q "logger name=\"$APP_PACKAGE\"" src/test/resources/logback.xml; then
        sed -i.tmp "/<root/i\\    <logger name=\"$APP_PACKAGE\" level=\"INFO\" />" src/test/resources/logback.xml
    fi
    
    rm -f src/test/resources/logback.xml.tmp
    echo "✅ 已优化现有的logback.xml配置"
fi

echo "测试日志优化完成！"
```

### 步骤 5：验证和测试运行

1. **编译验证：**
```bash
# 清理并编译
mvn clean compile test-compile -q

# 检查编译错误
if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
else
    echo "❌ 编译失败，请检查迁移结果"
    exit 1
fi
```

2. **运行测试验证：**
```bash
# 运行所有测试
mvn test -Dspring.profiles.active=test

# 生成测试报告
mvn surefire-report:report

# 检查测试结果
echo "=== 测试结果统计 ==="
find ./ -name "TEST-*.xml" | xargs grep -h "tests=" | awk -F'"' '{sum+=$2} END {print "总测试数: " sum}'
find ./ -name "TEST-*.xml" | xargs grep -h "failures=" | awk -F'"' '{sum+=$2} END {print "失败数: " sum}'
find ./ -name "TEST-*.xml" | xargs grep -h "errors=" | awk -F'"' '{sum+=$2} END {print "错误数: " sum}'
```

3. **迁移完成检查：**
```bash
#!/bin/bash
# migration-verification.sh

echo "=== JUnit5迁移完成验证 ==="

# 检查是否还有JUnit4残留
JUNIT4_REMAINING=$(find ./ -name "*.java" -type f | xargs grep -l "import org.junit.Test" | wc -l)
if [ $JUNIT4_REMAINING -eq 0 ]; then
    echo "✅ 所有JUnit4导入已清理完成"
else
    echo "❌ 仍有 $JUNIT4_REMAINING 个文件包含JUnit4导入"
    find ./ -name "*.java" -type f | xargs grep -l "import org.junit.Test"
fi

# 检查JUnit5使用情况
JUNIT5_TESTS=$(find ./ -name "*.java" -type f | xargs grep -l "import org.junit.jupiter.api.Test" | wc -l)
echo "✅ JUnit5测试文件数量: $JUNIT5_TESTS"

# 检查依赖
if grep -q "junit-jupiter" pom.xml; then
    echo "✅ JUnit5依赖已正确配置"
else
    echo "❌ 缺少JUnit5依赖"
fi

echo "迁移验证完成！"

# 提交迁移结果
echo "📝 提交迁移结果..."
git add .
git commit -m "Migrate from JUnit4 to JUnit5

- Replace JUnit4 imports with JUnit5 equivalents
- Update annotations (@Before -> @BeforeEach, etc.)
- Replace Assert with Assertions
- Remove @RunWith annotations
- Update Maven dependencies
- Ensure Spring Boot test compatibility"

echo "✅ 迁移已提交到分支: $MIGRATION_BRANCH"
echo "💡 如需合并到主分支，请运行: git checkout main && git merge $MIGRATION_BRANCH"
```

## 完整迁移执行脚本

将以上所有脚本整合为一个完整的迁移脚本：

```bash
#!/bin/bash
# complete-junit5-migration.sh

set -e  # 遇到错误立即退出

echo "🚀 开始JUnit4到JUnit5完整迁移流程..."

# 步骤1: Git状态检查和分支创建
echo "📦 检查Git状态并创建迁移分支..."
if ! git diff --quiet || ! git diff --cached --quiet; then
    echo "❌ Git工作区不干净，请先提交所有更改"
    git status --porcelain
    exit 1
fi

MIGRATION_BRANCH="feature/junit5-migration-$(date +%Y%m%d-%H%M%S)"
git checkout -b "$MIGRATION_BRANCH"
echo "✅ 已创建迁移分支: $MIGRATION_BRANCH"

# 步骤2: 分析现状
echo "🔍 分析项目现状..."
find ./ -name "*.java" -type f | xargs grep -l "import org.junit.Test" > junit4-files.txt
echo "发现 $(wc -l < junit4-files.txt) 个JUnit4测试文件"

# 步骤3: 更新依赖
echo "📝 更新Maven依赖..."
# 执行依赖更新脚本内容...

# 步骤4: 迁移代码
echo "🔄 执行代码迁移..."
# 执行代码迁移脚本内容...

# 步骤4.5: 优化测试日志
echo "📝 优化测试日志配置..."
# 执行日志优化脚本内容...

# 步骤5: 验证结果
echo "✅ 验证迁移结果..."
mvn clean compile test-compile -q
mvn test -Dspring.profiles.active=test

echo "🎉 JUnit5迁移完成！"
echo "📊 请查看测试报告确认所有测试正常运行"

# 提交迁移结果
echo "📝 提交迁移结果..."
git add .
git commit -m "Migrate from JUnit4 to JUnit5

- Replace JUnit4 imports with JUnit5 equivalents
- Update annotations (@Before -> @BeforeEach, etc.)
- Replace Assert with Assertions
- Remove @RunWith annotations
- Update Maven dependencies
- Ensure Spring Boot test compatibility"

echo "✅ 迁移已提交到分支: $MIGRATION_BRANCH"
echo "💡 如需合并到主分支，请运行: git checkout main && git merge $MIGRATION_BRANCH"
```

## 最终指令

记住，您的任务是完成JUnit4到JUnit5的完整迁移。执行以下完整流程：

1. **Git检查**：确保工作区干净，创建迁移分支
2. **分析现状**：识别所有需要迁移的文件和依赖
3. **更新依赖**：移除JUnit4，添加JUnit5依赖
4. **自动化迁移**：使用shell脚本批量替换代码
4.5. **优化日志**：配置测试日志为极简格式，便于AI阅读
5. **验证测试**：确保所有测试在JUnit5环境下正常运行

**重要提醒：**
- **必须使用提供的shell脚本进行自动化迁移**，这是最高效和一致的方式
- **完全移除JUnit4**：不能留下任何JUnit4的痕迹
- **验证完整性**：迁移后必须确保所有测试都能正确运行
- **Spring Boot兼容**：确保迁移后的测试与Spring Boot完全兼容

您的最终输出应该是一个完全迁移到JUnit5的项目，所有测试都能在JUnit5环境下正确运行，并且完全移除了JUnit4的依赖和代码。