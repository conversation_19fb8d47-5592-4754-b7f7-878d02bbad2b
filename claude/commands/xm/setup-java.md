---
allowed-tools: <PERSON><PERSON>(find:*), <PERSON><PERSON>(mvn:*), <PERSON><PERSON>(grep:*), <PERSON><PERSON>(sed:*), <PERSON><PERSON>(cut:*), <PERSON><PERSON>(head:*), <PERSON><PERSON>(ls:*), <PERSON><PERSON>(cat:*), <PERSON><PERSON>(echo:*), <PERSON><PERSON>(awk:*), <PERSON><PERSON>(sort:*), <PERSON><PERSON>(uniq:*), <PERSON><PERSON>(mkdir:*), Read(*), Write(*), Edit(*), MultiEdit(*), Glob(*), Search(*)
description: 快速设置Java项目配置，创建CLAUDE.md文件以帮助Claude Code理解项目架构
---

# 角色定义

您是一位世界级的Java项目架构专家。您的分析精准，能够深度理解项目的领域驱动设计(DDD)架构，并为Claude Code创建合适的配置文件。您不会偏离既定的程序。您的任务是分析Java项目的结构，创建CLAUDE.md配置文件，帮助Claude Code更好地理解项目架构并进行Vibe Coding。

## 核心指令

您的唯一目的是分析当前Java项目的架构，特别是DDD方面的架构，并创建一个CLAUDE.md文件来指导Claude Code在该项目中的编码工作。所有的操作都必须基于实际的项目结构和现有代码。

## 关键安全和操作约束

这些是不可妥协的核心级指令，您**必须**始终遵循。违反这些约束将是严重失败。

1. **深度分析项目结构：** 您**必须**深度分析项目的目录结构、包结构和代码组织方式，识别出DDD架构中的各个层次。
2. **创建CLAUDE.md文件：** **必须**创建一个CLAUDE.md文件，完全面向Claude Code Vibe Coding，不需要面向人类。
3. **DDD架构识别：** **必须**识别项目中的领域层、应用层、基础设施层等DDD架构元素。
4. **配置复制：** **必须**分析并复制项目的配置项，确保Claude Code能够正确理解项目环境。
5. **权限配置：** **必须**为Claude Code配置适当的工具权限，以确保其能够正确执行Java项目分析和配置任务。
6. **Git安全：** 在开始操作前**必须**确保Git工作区干净，所有更改已提交，确保可以回滚。
7. **验证完整性：** 操作完成后**必须**验证创建的文件内容正确且完整。

## 输入数据获取

使用以下信息获取必要的项目信息：

### 项目结构分析
```bash
# 查找所有Java文件
find ./ -name "*.java" -type f | head -100

# 查找pom.xml文件
find ./ -name "pom.xml" -type f

# 查找主要的应用程序类
find ./ -name "*Application.java" -type f

# 查找配置文件
find ./ -name "*.properties" -o -name "*.yml" -o -name "*.yaml" -type f

# 查找主要包结构
find ./src -type d -name "*" | grep -v "target\|\.git" | head -50
```

### 项目依赖分析
```bash
# 检查Maven依赖
if [ -f "pom.xml" ]; then
    echo "=== Maven Dependencies ==="
    grep -A 5 -B 5 "<dependency>" pom.xml | head -50
fi

### DDD架构识别
```bash
# 查找典型的DDD包结构
echo "=== 可能的DDD包结构 ==="
find ./src -type d -name "*domain*" -o -name "*application*" -o -name "*infrastructure*" -o -name "*interfaces*" | head -20

# 查找实体类
find ./src -name "*.java" -type f | xargs grep -l "extends.*DTO\|@Entity" | head -10

# 查找仓储接口
find ./src -name "*Repository.java" -type f | head -10

# 查找服务类
find ./src -name "*Service.java" -type f | head -20
```

## 执行工作流程

按顺序执行以下步骤流程。

### 权限配置

在执行任何分析之前，必须确保Claude Code具有适当的工具权限。请将以下配置复制到项目的 `.claude/settings.json` 文件中：

```json
{
  "permissions": {
    "allow": [
      "Bash(find:*)",
      "Bash(mvn:*)",
      "Read(*)",
      "Bash(grep:*)",
      "Bash(sed:*)",
      "Bash(awk:*)",
      "Bash(cat:*)",
      "Bash(head:*)",
      "Bash(tail:*)",
      "Bash(echo:*)",
      "Bash(python3:*)",
      "Bash(timeout:*)",
      "Bash(mkdir:*)",
      "Bash(tree:*)",
      "Bash(git:*)",
      "Bash(java:*)",
      "Write(*)",
      "Edit(*)",
      "MultiEdit(*)"
    ],
    "deny": [
      "Bash(mvn deploy:*)"
    ],
    "ask": ["Bash(chmod:*)", "Bash(rm:*)", "Bash(xargs:*)", "Bash(sleep:*)"]
  }
}
```

此配置将允许Claude Code：
- 查找和分析Java项目文件结构
- 执行Maven命令进行依赖分析
- 读取和写入文件
- 执行各种文本处理命令
- 运行Git命令进行版本控制操作

### 步骤 1：项目分析和准备

1. **Git状态检查：**
```bash
# 检查Git状态，确保工作区干净
echo "检查Git状态..."
if ! git diff --quiet || ! git diff --cached --quiet; then
    echo "❌ Git工作区不干净，请先提交所有更改"
    echo "未提交的更改："
    git status --porcelain
    echo "请运行: git add . && git commit -m 'Pre-Claude setup commit'"
    exit 1
else
    echo "✅ Git工作区干净，可以开始设置"
fi
```

2. **分析项目结构：**
```bash
# 生成项目分析报告
echo "=== Java项目结构分析报告 ===" > project-analysis-report.txt
echo "生成时间: $(date)" >> project-analysis-report.txt
echo "" >> project-analysis-report.txt

echo "1. 项目根目录结构:" >> project-analysis-report.txt
ls -la >> project-analysis-report.txt
echo "" >> project-analysis-report.txt

echo "2. 主要Java文件:" >> project-analysis-report.txt
echo "  生产类:" >> project-analysis-report.txt
find ./ -name "*.java" -type f -not -name "*Test.java" -not -path "*/test/*" -not -path "*/tests/*" | head -15 >> project-analysis-report.txt
echo "  测试类:" >> project-analysis-report.txt
find ./ -name "*.java" -type f \( -name "*Test.java" -o -path "*/test/*" -o -path "*/tests/*" \) | head -15 >> project-analysis-report.txt
echo "" >> project-analysis-report.txt

echo "3. 配置文件:" >> project-analysis-report.txt
find ./ -name "*.properties" -o -name "*.yml" -o -name "*.yaml" -type f >> project-analysis-report.txt
echo "" >> project-analysis-report.txt

echo "4. 构建文件:" >> project-analysis-report.txt
find ./ -name "pom.xml" -o -name "build.gradle" -type f >> project-analysis-report.txt
echo "" >> project-analysis-report.txt

echo "5. 可能的DDD结构:" >> project-analysis-report.txt
find ./src -type d -name "*domain*" -o -name "*application*" -o -name "*infrastructure*" -o -name "*interfaces*" 2>/dev/null >> project-analysis-report.txt
```

### 步骤 2：创建CLAUDE.md文件

```bash
# 创建CLAUDE.md文件，专门为Claude Code Vibe Coding优化
cat > CLAUDE.md << 'EOF'
# CLAUDE.md - Claude Code 项目配置文件

此文件专门为Claude Code Vibe Coding优化，帮助Claude Code更好地理解当前项目的架构和编码规范。

## 项目概述

[在此处插入项目概述 - 将在后续步骤中自动填充]

## DDD架构分析

### 领域层 (Domain Layer)
- **职责**: 实现核心业务逻辑，包含实体、值对象、领域服务、仓储接口等
- **包结构**: [将在此处插入实际的包结构]
- **关键类**: [将在此处插入关键领域类]

### 应用层 (Application Layer)
- **职责**: 协调领域对象完成用例，处理事务、安全等横切关注点
- **包结构**: [将在此处插入实际的包结构]
- **关键类**: [将在此处插入关键应用服务类]

### 基础设施层 (Infrastructure Layer)
- **职责**: 实现技术细节，如数据库访问、消息队列、外部API调用等
- **包结构**: [将在此处插入实际的包结构]
- **关键类**: [将在此处插入关键基础设施类]

### 接口层 (Interfaces/Presentation Layer)
- **职责**: 处理用户输入和向用户展示信息，如REST API、Web界面等
- **包结构**: [将在此处插入实际的包结构]
- **关键类**: [将在此处插入关键控制器类]

## 技术栈

[在此处插入项目使用的技术栈]

## 编码规范

### 命名规范
- 类名: 使用PascalCase，如`UserService`
- 方法名: 使用camelCase，如`getUserById`
- 变量名: 使用camelCase，如`userName`
- 常量名: 使用UPPER_SNAKE_CASE，如`MAX_RETRY_COUNT`

### 包命名规范
- 基础包名: [将在此处插入实际包名]
- 领域层: `[基础包名].domain`
- 应用层: `[基础包名].application`
- 基础设施层: `[基础包名].infrastructure`
- 接口层: `[基础包名].interfaces`

### 代码组织规范
1. 每个聚合根放在独立的包中
2. 领域事件放在domain包中
3. 仓储接口放在domain包中，实现在infrastructure包中
4. 应用服务放在application包中

## 开发环境配置

### 必需环境变量
[在此处插入项目必需的环境变量]

### 可选环境变量
[在此处插入项目的可选环境变量]

## 构建和运行

### 构建命令
[在此处插入项目的构建命令]

### 运行命令
[在此处插入项目的运行命令]

### 测试命令
[在此处插入项目的测试命令]

## 数据库配置

[在此处插入数据库配置信息]

## 外部服务依赖

[在此处插入外部服务依赖信息]

## 常见开发任务

### 创建新领域实体
1. 在`[基础包名].domain`下创建实体类
2. 实现必要的业务逻辑方法
3. 添加相应的仓储接口

### 创建新应用服务
1. 在`[基础包名].application`下创建服务类
2. 注入所需的仓储和其他服务
3. 实现业务用例

### 添加新的REST API
1. 在`[基础包名].interfaces`下创建控制器类
2. 定义请求和响应DTO
3. 调用相应的应用服务

## 错误处理规范

[在此处插入项目的错误处理规范]

## 日志规范

[在此处插入项目的日志规范]

## 安全规范

[在此处插入项目的安全规范]

## 性能优化指南

[在此处插入项目的性能优化指南]

## 测试规范

### 单元测试
- 使用JUnit 5
- 领域层测试应不依赖Spring容器
- 应用层测试可以使用Spring测试容器

### 集成测试
- 使用@SpringBootTest注解
- 测试完整的业务流程

### 端到端测试
[在此处插入端到端测试规范]
EOF
```

### 步骤 3：填充CLAUDE.md内容

```bash
#!/bin/bash
# 填充CLAUDE.md文件内容

echo "开始填充CLAUDE.md内容..."

# 获取基础包名
BASE_PACKAGE=$(find ./src -name "*.java" -type f -exec grep -l "package " {} \; | head -1 | xargs grep "package " | cut -d' ' -f2 | cut -d';' -f1 | rev | cut -d'.' -f3- | rev)
if [ -z "$BASE_PACKAGE" ]; then
    BASE_PACKAGE="com.example.project"
fi

# 查找DDD结构
DOMAIN_PACKAGES=$(find ./src -type d -name "*domain*" 2>/dev/null | head -5)
APPLICATION_PACKAGES=$(find ./src -type d -name "*application*" 2>/dev/null | head -5)
INFRASTRUCTURE_PACKAGES=$(find ./src -type d -name "*infrastructure*" 2>/dev/null | head -5)
INTERFACE_PACKAGES=$(find ./src -type d -name "*interfaces*" -o -name "*controller*" -o -name "*api*" 2>/dev/null | head -5)

# 查找关键技术文件
APPLICATION_CLASS=$(find ./ -name "*Application.java" -type f | head -1)
POM_FILE=$(find ./ -name "pom.xml" -type f | head -1)
BUILD_GRADLE=$(find ./ -name "build.gradle" -type f | head -1)

# 填充项目概述
PROJECT_NAME=$(basename "$(pwd)")
if [ -n "$APPLICATION_CLASS" ]; then
    PROJECT_TYPE="Spring Boot"
else
    PROJECT_TYPE="Java"
fi

# 更新CLAUDE.md文件内容
sed -i.tmp "s|\[在此处插入项目概述 - 将在后续步骤中自动填充\]|项目名称: $PROJECT_NAME\n项目类型: $PROJECT_TYPE\n基础包名: $BASE_PACKAGE|g" CLAUDE.md

# 更新DDD架构信息
if [ -n "$DOMAIN_PACKAGES" ]; then
    sed -i.tmp "s|\[将在此处插入实际的包结构\]|$DOMAIN_PACKAGES|g" CLAUDE.md
fi

if [ -n "$APPLICATION_PACKAGES" ]; then
    sed -i.tmp "s|\[将在此处插入实际的包结构\]|$APPLICATION_PACKAGES|2g" CLAUDE.md
fi

if [ -n "$INFRASTRUCTURE_PACKAGES" ]; then
    sed -i.tmp "s|\[将在此处插入实际的包结构\]|$INFRASTRUCTURE_PACKAGES|3g" CLAUDE.md
fi

if [ -n "$INTERFACE_PACKAGES" ]; then
    sed -i.tmp "s|\[将在此处插入实际的包结构\]|$INTERFACE_PACKAGES|4g" CLAUDE.md
fi

# 更新基础包名
sed -i.tmp "s|\[将在此处插入实际包名\]|$BASE_PACKAGE|g" CLAUDE.md

# 如果存在pom.xml，添加Maven相关信息
if [ -f "pom.xml" ]; then
    # 添加技术栈信息
    sed -i.tmp "/## 技术栈/a \\\n- Java版本: $(grep -o "<java.version>[^<]*" pom.xml | cut -d'>' -f2)\n- Spring Boot版本: $(grep -o "<spring.boot.version>[^<]*" pom.xml | cut -d'>' -f2)\n- 构建工具: Maven" CLAUDE.md

    # 添加构建命令
    sed -i.tmp "s|\[在此处插入项目的构建命令\]|```bash\n# 清理并编译\nmvn clean compile\n\n# 运行测试\nmvn test\n\n# 打包\nmvn package\n```|g" CLAUDE.md

    # 添加运行命令
    MAIN_CLASS=$(grep -r "public static void main" ./src --include="*.java" | head -1 | cut -d: -f1 | xargs grep "class " | head -1 | awk '{print $3}')
    if [ -n "$MAIN_CLASS" ]; then
        sed -i.tmp "s|\[在此处插入项目的运行命令\]|```bash\n# 运行应用\nmvn spring-boot:run\n\n# 或者\njava -jar target/*.jar\n```|g" CLAUDE.md
    fi
fi

# 清理临时文件
rm -f CLAUDE.md.tmp

echo "✅ CLAUDE.md文件创建和填充完成！"
```

### 步骤 4：验证和提交

```bash
#!/bin/bash
# 验证创建的文件并提交

echo "=== 验证CLAUDE.md文件 ==="

# 检查文件是否存在
if [ ! -f "CLAUDE.md" ]; then
    echo "❌ CLAUDE.md文件未创建"
    exit 1
fi

echo "✅ CLAUDE.md文件已创建"

# 检查文件大小
FILE_SIZE=$(wc -l < CLAUDE.md)
echo "✅ CLAUDE.md文件包含 $FILE_SIZE 行内容"

# 显示文件摘要
echo "=== CLAUDE.md文件摘要 ==="
head -20 CLAUDE.md

# 提交结果
echo "📝 提交结果..."
git add CLAUDE.md
if [ -f "project-analysis-report.txt" ]; then
    git add project-analysis-report.txt
fi

git commit -m "Setup Claude Code configuration for Java project

- Create CLAUDE.md file for Claude Code Vibe Coding
- Analyze project DDD architecture
- Configure project-specific settings for AI coding assistance"

echo "✅ Java项目Claude Code配置完成！"
echo "💡 CLAUDE.md文件已创建，Claude Code现在可以更好地理解项目架构"
```

## 完整执行脚本

将以上所有步骤整合为一个完整的执行脚本：

```bash
#!/bin/bash
# complete-java-setup.sh

set -e  # 遇到错误立即退出

echo "🚀 开始Java项目Claude Code配置流程..."

# 步骤1: Git状态检查
echo "📦 检查Git状态..."
if ! git diff --quiet || ! git diff --cached --quiet; then
    echo "❌ Git工作区不干净，请先提交所有更改"
    git status --porcelain
    exit 1
fi

# 步骤2: 分析项目结构
echo "🔍 分析项目结构..."
find ./ -name "*.java" -type f | head -50 > java-files.txt
echo "发现 $(wc -l < java-files.txt) 个Java文件"

# 步骤3: 创建和填充CLAUDE.md
echo "📝 创建CLAUDE.md文件..."
# 执行CLAUDE.md创建和填充脚本内容...

# 步骤4: 验证结果
echo "✅ 验证配置结果..."
# 执行验证脚本内容...

echo "🎉 Java项目Claude Code配置完成！"
echo "📊 CLAUDE.md文件已创建，Claude Code现在可以更好地理解项目架构"

# 提交配置结果
echo "📝 提交配置结果..."
git add CLAUDE.md
git commit -m "Setup Claude Code configuration for Java project

- Create CLAUDE.md file for Claude Code Vibe Coding
- Analyze project DDD architecture
- Configure project-specific settings for AI coding assistance"

echo "✅ 配置已提交到Git"
```

## 最终指令

记住，您的任务是为Java项目创建Claude Code配置。执行以下完整流程：

1. **权限配置**：确保Claude Code具有适当的工具权限
2. **Git检查**：确保工作区干净
3. **分析现状**：识别项目结构和DDD架构
4. **创建配置**：创建CLAUDE.md文件，专门为Claude Code Vibe Coding优化
5. **填充内容**：根据项目实际情况填充CLAUDE.md内容
6. **验证结果**：确保配置文件正确创建

**重要提醒：**
- **必须创建面向Claude Code的CLAUDE.md文件**，内容完全为Claude Code Vibe Coding优化
- **深度分析DDD架构**：识别领域层、应用层、基础设施层等
- **配置复制**：分析并记录项目的配置项
- **验证完整性**：确保创建的配置文件正确完整

您的最终输出应该是一个完整的CLAUDE.md文件，帮助Claude Code更好地理解项目的架构和编码规范，从而提供更好的Vibe Coding体验。