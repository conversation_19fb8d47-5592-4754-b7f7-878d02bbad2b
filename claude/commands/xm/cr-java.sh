#!/bin/bash

# 代码审查前置脚本 - 确保获取最新的默认分支代码
# 该脚本会先切换到默认分支，拉取最新代码，然后切换回当前分支进行代码审查

set -e  # 遇到错误时退出

echo "🔍 开始执行代码审查前置操作..."

# 保存当前分支
CURRENT_BRANCH=$(git branch --show-current)
echo "📍 当前分支: $CURRENT_BRANCH"

# 获取默认分支名称
DEFAULT_BRANCH=$(git remote show origin | grep 'HEAD branch' | cut -d' ' -f5)
echo "📍 默认分支: $DEFAULT_BRANCH"

# 检查是否在默认分支上
if [ "$CURRENT_BRANCH" = "$DEFAULT_BRANCH" ]; then
    echo "⚠️  当前已在默认分支上，直接拉取最新代码..."
    git pull origin "$DEFAULT_BRANCH"
else
    echo "🔄 切换到默认分支并拉取最新代码..."
    # 切换到默认分支
    git checkout "$DEFAULT_BRANCH"

    # 拉取最新代码
    git pull origin "$DEFAULT_BRANCH"

    # 切换回原分支
    echo "🔄 切换回原分支: $CURRENT_BRANCH"
    git checkout "$CURRENT_BRANCH"
fi

echo "✅ 代码审查前置操作完成，现在可以安全地进行代码审查了。"

# 执行代码审查
echo "🚀 启动代码审查..."
claude-code --command xm,cr-java