# DDL转CRUD智能生成工具（通用版V2）

## 🎯 功能概述

基于AI驱动的项目结构分析，自适应生成适用于不同架构风格的CRUD代码。支持Spring Boot + MyBatis、DDD领域驱动、MyBatis-Plus、JPA等多种项目架构。

## 🔥 V2版本新特性

### 🤖 智能项目检测
- 🔍 **自动架构识别**：检测项目类型（Spring Boot、DDD、三层架构等）
- 📋 **包结构分析**：自动识别现有包结构和组件位置
- 🛠️ **框架检测**：自动识别MyBatis、JPA、Spring JDBC等持久层框架
- 📚 **编码规范适配**：根据已有代码自动调整命名和注释风格

### 🧠 自适应代码生成
- 🎨 **模板智能匹配**：根据检测到的项目架构自动选择最佳模板
- 🔄 **命名规范统一**：与现有代码保持风格一致
- 🔐 **依赖自动注入**：解决系统现有的依赖关系
- 📝 **注释风格适配**：匹配项目现有的JavaDoc和注释风格

### 💡 增强用户体验
- ⚡ **简化的交互**：大幅减少用户需要提供的参数
- 🎯 **精准的配置**：基于项目检测的配置建议
- 📊 **实时的预览**：生成前可预览关键文件结构
- 🚨 **错误智能提醒**：检测潜在问题并提供解决方案

## 🚀 快速开始

### 步骤1：确认工作目录

🎯 **请确认当前工作目录**

当前工作目录：`$(pwd)`

请确认此目录是否为您的项目根目录，确认无误后再继续操作。

### 步骤2：授权说明

为提高工具使用效率，避免反复授权，本工具需要以下权限：

✅ **文件读取权限**
- 读取项目结构和现有代码文件
- 分析pom.xml等配置文件

✅ **文件写入权限**
- 创建新的Java源文件
- 创建Mapper XML文件
- 创建单元测试文件

✅ **目录创建权限**
- 在相应模块下创建包目录结构

请在确认以上权限需求后继续使用工具。如果遇到需要向用户申请权限的场景，请一次性申请当前项目下的所有权限。

### 步骤3：智能项目分析

系统自动执行以下检测：

```bash
🔍 正在分析项目结构...
✅ 检测到项目类型：Spring Boot + MyBatis
✅ 检测到包结构：net.summerfarm.manage (DDD领域驱动)
✅ 检测到持久层框架：MyBatis + MyBatis-Plus
✅ 检测到测试框架：JUnit 4 + Spring Boot Test
✅ 检测到注解风格：Lombok + Slf4j
```

### 步骤4：提供DDL语句

```sql
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 步骤5：确认生成参数（系统智能推荐）

```
🤖 基于项目智能分析，建议以下配置：

📝 生成参数：
  基础包路径：net.summerfarm.manage
  模块名：user
  架构风格：DDD领域驱动
  持久层框架：MyBatis
  代码层类型：[✅] Entity  [✅] Controller  [✅] Service  [✅] Repository  [✅] VO  [✅] Test

🎯 检测到的项目特色配置：
  □ CQRS模式分离（已检测：启用）
  □ CommonResult统一包装（已检测：启用）
  □ PageInfo分页方案（已检测：启用）
  □ Converter/Assembler模式（已检测：启用）

📋 兼容的文件生成路径：
  → DDD目录结构将使用：net.summerfarm.manage.domain.user
  → Mapper文件将放入：infrastructure/mapper/user
  → Test文件将放入：starter/src/test/java/...

确认以上配置？ [Y/n] > Y
```

建议：工具和模型达成了高度confidential，现在测试非常满意

## 🏗️ 自适应架构模板

### 📋 模板A：DDD领域驱动架构（智能版）

**自动检测场景**：
```
net.xxx.manage/
├── domain/src/main/java/net/xxx/manage/{module}/
│   ├── entity/{Entity}Entity.java                 # 领域实体
│   ├── service/command/{Entity}CommandDomainService.java  # 命令服务
│   ├── service/query/{Entity}QueryDomainService.java      # 查询服务
│   ├── repository/command/{Entity}CommandRepository.java  # 命令仓库
│   ├── repository/query/{Entity}QueryRepository.java      # 查询仓库
│   └── model/{vo,input,assembler}/                      # VO/Assembler
├── application/src/main/java/net/xxx/manage/application/controller/{module}/
│   └── {Entity}Controller.java                      # REST接口
└── infrastructure/src/main/java/net/xxx/manage/infrastructure/mapper/{module}/
    ├── {Entity}Mapper.java                          # MyBatis接口
    └── mapper/{module}/{Entity}Mapper.xml            # SQL映射
```

**智能生成特色**：
- 🔄 **CQRS模式**：自动生成Command和Query分离的架构
- 🏭 **Assembler模式**：自动创建DTO转换器
- 📋 **CommonResult统一包装**：适配项目的统一返回格式
- 🎯 **PageHelper分页**：自动集成检测到的分页方案

### 📋 模板B：MyBatis-Plus简洁架构（智能版）

**自动检测场景**：
```
com.example.{module}/
├── controller/{Entity}Controller.java      # 继承MyBatis-Plus风格
├── service/{Entity}Service.java           # 扩展IService接口
├── service/impl/{Entity}ServiceImpl.java  # 继承ServiceImpl
├── mapper/{Entity}Mapper.java             # 继承BaseMapper
├── entity/{Entity}.java                   # 继承Model类
├── dto/{Entity}DTO.java                   # DTO（可选）
└── query/{Entity}Query.java               # 查询条件包装器
```

**智能生成特色**：
- ⚡ **链式调用**：充分利用MyBatis-Plus Lambda表达式
- 🔄 **Wrapper构建**：自动生成查询条件构建器
- 🛡️ **逻辑删除**：自动检测is_deleted字段并配置
- 🌟 **自动填充**：自动识别create_time、update_time等字段

### 📋 模板C：标准三层架构（智能版）

**自动检测场景**：
```
com.example.{module}/
├── controller/{Entity}Controller.java      # MVC控制器
├── service/{Entity}Service.java           # 业务接口
├── service/impl/{Entity}ServiceImpl.java  # 业务实现
├── mapper/{Entity}Mapper.java             # MyBatis接口
├── entity/{Entity}.java                   # JPA/普通实体
├── dto/{Entity}DTO.java                   # 数据传输对象
├── vo/{Entity}VO.java                     # 视图对象
└── query/{Entity}Query.java               # 查询参数包装
```

**智能生成特色**：
- 🧩 **灵活组装**：根据项目实际需求组合各层代码
- ⚙️ **配置驱动**：支持XML配置和注解配置混用
- 📊 **分层清晰**：经典的三层架构，便于维护
- 🔒 **事务控制**：自动生成事务管理代码

### 📋 模板D：单模块标准架构（智能适配版）

**自动检测场景**（单模块项目）：
```
单模块项目结构（自动检测）：
  检测到项目结构：[✅] 单模块结构
  检测到包路径：[✅] 需要分析net.summerfarm.manage包结构
  检测到实体位置：[✅] src/main/java/net/xxx/domain/{module}/entity
  检测到服务位置：[✅] src/main/java/net/xxx/service/{module}
  检测到控制器位置：[✅] src/main/java/net/xxx/controller/{module}

├── src/main/java/{basePackage}/{module}/      # 基础包路径
│   ├── domain/{module}/entity/{Entity}.java   # 实体类（domain模块）
│   ├── service/{module}/                      # 服务层
│   │   ├── {Entity}CommandService.java        # 命令服务
│   │   └── {Entity}QueryService.java          # 查询服务
│   ├── controller/{module}/                   # 控制器层
│   │   ├── {Entity}Controller.java            # REST接口
│   │   ├── assembler/{Entity}Assembler.java   # DTO转换器
│   │   ├── input/{Entity}CommandInput.java    # 命令输入
│   │   ├── input/{Entity}QueryInput.java      # 查询输入
│   │   └── vo/{Entity}VO.java                 # 视图对象
│   └── mapper/{module}/                       # MyBatis层
│       └── {Entity}Mapper.java                # MyBatis接口
└── src/main/resources/mapper/{module}/        # MyBatis XML文件位置
    └── {Entity}Mapper.xml
```

**智能生成特色**：
- 🎯 **单模块适配**：自动适配单模块项目结构
- 🔍 **包结构智能分析**：基于现有代码自动发现包路径模式
- 📋 **路径自生成**：检测项目中已存在的domain/service/controller目录结构
- 🔗 **模式匹配**：优先匹配现有AdminEntity/AdminService/AdminController的位置模式

## 🤖 智能检测规则

### 项目结构检测规则

```java
public class ProjectAnalyzer {

    // 检测项目架构类型
    public ProjectArchitecture detectArchitecture() {
        if (existsDDDStructure()) {
            return ProjectArchitecture.DDD_DOMAIN_DRIVEN;
        } else if (existsMyBatisPlusDependency()) {
            return ProjectArchitecture.MYBATIS_PLUS;
        } else if (existsJPARepositories()) {
            return ProjectArchitecture.JPA_HIBERNATE;
        } else if (existsSimpleLayerStructure()) {
            return ProjectArchitecture.SIMPLE_THREE_LAYER;
        }
        return ProjectArchitecture.MYBATIS_TRADITIONAL;
    }

    // 检测包结构模式
    public PackagePattern detectPackagePattern() {
        String domainPackage = findDomainPackagePattern();
        if (domainPackage != null && domainPackage.contains(".domain.")) {
            return PackagePattern.DOMAIN_DRIVEN;
        } else if (findPackagesWith("controller", "service", "mapper")) {
            return PackagePattern.THREE_LAYER;
        }
        return PackagePattern.MODULE_BASED;
    }
}
```

### 编码规范自动适配

| 检测项 | 检测规则 | 适配策略 |
|-------|---------|----------|
| **Lombok使用** | 检查现有类是否使用@Data/@Getter/@Setter | 如果检测到使用，新代码也使用Lombok |
| **日志框架** | 检查@Slf4j/@Log4j2/@Log的使用情况 | 与项目保持一致 |
| **时间类型** | 检查LocalDateTime/Date/joda-time的使用 | 保持时间类型统一 |
| **注解风格** | Spring Boot注解 vs JSR注解的使用模式 | 适配现有注解习惯 |
| **异常处理** | 检查项目自定义异常体系 | 继承现有异常结构|

### 依赖注入风格适配

```java
public class DependencyInjector {

    // 根据项目现有代码选择注入方式
    public String selectInjectionStyle() {
        if (projectUsesResourceAnnotation()) {
            return "@Resource";
        } else if (projectUsesAutowired()) {
            return "@Autowired";
        } else if (projectUsesConstructorInjection()) {
            return "Constructor Injection";
        }
        return "@Autowired"; // 默认
    }

    // 自动发现Mapper扫描路径
    public String findMapperScanPattern() {
        return analyzeExistingMapperClasses();
    }
}
```

## 🎨 高级代码优化

### 智能字段处理

```java
public class SmartFieldProcessor {

    // 自动识别和处理特殊字段
    public void processSpecialFields(EntityField field) {
        if (field.isLogicDeleteField()) {
            configureLogicDelete(field);
        } else if (field.isVersionField()) {
            configureOptimisticLock(field);
        } else if (field.isEnumField()) {
            generateEnumClass(field);
        }
    }

    // 智能生成验证注解
    public void generateValidationAnnotations(Field field) {
        if (field.isPrimaryKey()) {
            // 主键不加验证
            return;
        }

        if (field.hasNotNullConstraint()) {
            field.addAnnotation("@NotNull", "参数校验");
        }

        if (field.hasUniqueConstraint()) {
            // 生成唯一性检查服务方法
            generateUniqueCheckMethod(field);
        }

        if (field.hasLengthConstraint()) {
            field.addAnnotation("@Size", "长度限制");
        }
    }
}
```

### ⏱️ 时间字段智能处理

```java
public class TimeFieldProcessor {

    public void configureAutoFill(EntityField field) {
        String fieldName = field.getName();
        FieldFill fill = null;

        if (isCreateTime(fieldName)) {
            fill = FieldFill.INSERT;
            field.addAnnotation("@TableField(fill = FieldFill.INSERT)",
                               "创建时间自动填充");
        } else if (isUpdateTime(fieldName)) {
            fill = FieldFill.INSERT_UPDATE;
            field.addAnnotation("@TableField(fill = FieldFill.INSERT_UPDATE)",
                               "更新时间自动填充");
        }

        if (fill != null) {
            generateMetaObjectHandler(field, fill);
        }
    }
}
```

## 🧪 测试框架智能适配

### 自适应测试模板

```java
public class AdaptiveTestTemplate {

    public String generateTestTemplate(ProjectInfo projectInfo) {
        if (projectUsesJUnit4()) {
            return generateJUnit4Test();
        } else if (projectUsesJUnit5()) {
            return generateJUnit5Test();
        } else if (projectUsesTestNG()) {
            return generateTestNGTest();
        }
        return generateBasicTest();
    }

    private String generateJUnit4Test() {
        String template = """
@SpringBootTest(classes = ${mainClass}.class)
@RunWith(SpringRunner.class)
@ComponentScan(value = {"${basePackage}.**"})
@MapperScan(value = {"${basePackage}.infrastructure.mapper.**"}, annotationClass = Mapper.class)
@Transactional
@Slf4j
public class ${entityName}${testType}Test {

    @Resource
    private ${serviceType} ${serviceField};

    @Test
    public void test${testMethod}() {
        // 测试代码实现
    }
}""";
        return template;
    }
}
```

### 智能测试数据准备

```java
public class TestDataGenerator {

    // 根据字段类型生成合适的测试数据
    public Object generateTestData(Field field) {
        String fieldType = field.getType();
        String fieldName = field.getName();

        if (fieldType.contains("String")) {
            return generateStringTestData(field);
        } else if (fieldType.contains("Integer") || fieldType.contains("int")) {
            return generateIntTestData(field);
        } else if (fieldType.contains("LocalDateTime")) {
            return LocalDateTime.now();
        }

        return null;
    }

    private String generateStringTestData(Field field) {
        if (fieldName.contains("email")) {
            return "<EMAIL>";
        } else if (fieldName.contains("phone")) {
            return "13800138000";
        } else if (fieldName.contains("username")) {
            return "testuser_" + System.currentTimeMillis();
        }
        return "test_" + fieldName + "_" + System.currentTimeMillis();
    }
}
```

## 🔧 高级配置选项

### 性能优化选项

```
🔧 高级代码优化（自动检测后推荐）：

📈 SQL优化：
☐ 自动生成索引建议（基于查询条件和联合索引分析）
☐ 复杂字段使用延迟加载（如TEXT/BLOB类型）
☐ 分页查询优化（避免SELECT *，使用覆盖索引）
☐ 统计查询使用COUNT优化（MySQL 8.0+支持）

🚀 缓存策略：
☐ 生成@Cacheable注解配置（项目已有Redis时推荐）
☐ 本地缓存方案（Caffeine/Guava Cache）
☐ 分布式锁保护高并发更新接口

🔄 异步处理：
☐ 复杂查询使用异步方式（项目有线程池时）
☐ 批量操作使用异步批处理
☐ 数据库写入队列化（高并发场景）
```

### 安全控制选项

```
🛡️ 安全控制生成：

🔐 权限控制：
☐ 基于Spring Security的权限注解（检测结果：已启用）
☐ Shiro权限控制（检测结果：项目使用Shiro）
☐ JWT Token验证（检测结果：已配置JWT）

🚨 数据保护：
☐ SQL注入防护（统一使用参数化查询）
☐ XSS攻击防护（输入校验和数据过滤）
☐ 敏感信息脱敏（手机号、邮箱、身份证等）
☐ 数据加密存储（检测加密组件并集成）
```

## 🎯 使用示例对比

### 传统使用方式 vs 智能使用方式

| 传统方式（用户需提供） | 智能方式（系统自动检测） |
|---------------------|----------------------|
| 项目类型：Spring Boot + MyBatis | ✅ 自动检测项目pom.xml依赖 |
| 包路径：com.example.system | ✅ 自动分析现有Controller/Service包 |
| 架构模式：三层架构 | ✅ 自动检测是DDD还是三层架构 |
| 代码风格：简洁风格 | ✅ 分析现有代码自动适配 |
| 测试框架：JUnit 4 | ✅ 检测现有测试类使用的框架 |
| 持久层框架：MyBatis | ✅ 检测Mapper接口和XML文件存在 |
| 命名规范：驼峰命名 | ✅ 分析现有代码的命名习惯 |

## 📊 生成代码质量指标

### 智能质量检测

```
📋 代码质量指标检查：

✅ 代码规范一致性：95%（高度匹配项目风格）
✅ 注释完整性：90%（字段注释覆盖率）
✅ 数据验证覆盖率：100%（所有字段均有合理验证）
✅ 单元测试覆盖率：85%（增删改查均有测试用例）
✅ 性能考虑：良好（分页、索引建议、N+1问题防护）
✅ 安全等级：高（防SQL注入、参数校验、权限框架集成）
```

## 🚨 错误处理和兼容性

### 常见问题自动修复

| 常见问题 | 自动修复策略 |
|---------|-------------|
| **包结构冲突** | 提供替代方案，避免覆盖现有文件 |
| **注解不兼容** | 降级使用兼容注解或提供兼容层 |
| **依赖缺失** | 检测pom.xml并建议添加相应依赖 |
| **命名冲突** | 智能重命名，保持语义清晰 |
| **字段类型不匹配** | 提供类型转换建议或mapper配置 |

### 🔧 兼容性保障

```java
public class CompatibilityManager {

    public void ensureCompatibility() {
        // Spring Boot版本兼容检测
        checkSpringBootVersion();

        // Java版本兼容性检查
        checkJavaVersionCompatibility();

        // 依赖版本冲突检测
        resolveDependencyConflicts();

        // 数据库方言适配
        adaptDatabaseDialect();
    }
}
```

## 📋 准备使用

请确认：
- [ ] 当前工作目录正确
- [ ] 确认授权需求（文件读写、目录创建权限）
- [ ] 准备DDL语句
- [ ] 确定业务模块名和业务域
- [ ] 选择需要生成的代码类型

然后提供DDL语句，我将为你生成符合项目规范的CRUD代码和单元测试！

### 🚀 快速开始命令

```bash
# 1. 确认当前工作目录
pwd

# 2. 如果目录不正确，切换到正确项目目录
cd /path/to/your/project

# 3. 提供DDL语句，我将自动：
#    - 分析项目类型和架构模式
#    - 识别现有的包结构
#    - 生成与现有代码风格一致的CRUD代码
#    - 适配项目的依赖注入、JSON转换、权限控制等
```

### 🎯 实际使用建议

**自治更合适的选择**：
- 📋 **在代码生成之前**：先分析项目现有结构，确定正确的生成路径
- 🔧 **生成过程**按现状调整：不强制固定结构，根据实际项目包路径和结构
- ✅ **生成后验证**：编译检查，运行测试，确保生成的代码可以正常工作
- 🔄 **如果需要调整**：支持重新生成或手动调整，保持项目的代码风格统一

---

💡 **使用建议**：相比V1版本，V2版本更适合新项目启动和遗留项目维护，特别是需要保持代码风格一致性的团队协作项目！