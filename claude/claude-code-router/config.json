{"LOG": true, "LOG_LEVEL": "debug", "CLAUDE_PATH": "", "HOST": "127.0.0.1", "PORT": 3456, "APIKEY": "", "API_TIMEOUT_MS": "600000", "PROXY_URL": "", "transformers": [], "Providers": [{"name": "xm", "api_base_url": "https://litellm-test.summerfarm.net/v1/chat/completions", "api_key": "sk-VVO9br4x5RsxWTMLnx0wjw", "models": ["deepseek-v3-1", "deepseek-r1-250528", "qwen3-coder", "kimi-k2", "kimi-k2-turbo"], "transformer": {"use": [["maxtoken", {"max_tokens": 16384}], "enhancetool"]}}, {"name": "openrouter", "api_base_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "${OPENROUTER_API_KEY}", "models": ["anthropic/claude-sonnet-4", "x-ai/grok-code-fast-1", "google/gemini-2.5-pro"], "transformer": {"use": ["openrouter", "maxtoken"]}}], "StatusLine": {"enabled": false, "currentStyle": "default", "default": {"modules": []}, "powerline": {"modules": []}}, "Router": {"default": "xm,qwen3-coder", "background": "xm,qwen3-coder", "think": "xm,deepseek-v3-1", "longContext": "xm,kimi-k2-turbo", "longContextThreshold": 60000, "webSearch": "openrouter,google/gemini-2.5-pro", "image": "openrouter,google/gemini-2.5-pro"}, "CUSTOM_ROUTER_PATH": ""}