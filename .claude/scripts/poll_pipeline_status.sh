#!/bin/bash

# =================================================================
# Poll Pipeline Status Script (v4 - with detailed output)
#
# Exit Codes:
#   0: Success
#   2: Timeout
#   3: Merge Conflict
#   4: Stage Failed or Canceled
#   5: Invalid Arguments
#
# STDOUT: On success or failure, outputs a concise reason string.
# STDERR: Outputs polling progress messages.
# =================================================================

# --- Argument Validation ---
if [ "$#" -ne 4 ]; then
    echo "Usage: $0 <ORGANIZATION_ID> <PIPELINE_ID> <PIPELINE_RUN_ID> <API_TOKEN>" >&2
    exit 5
fi

ORGANIZATION_ID=$1
PIPELINE_ID=$2
PIPELINE_RUN_ID=$3
API_TOKEN=$4

# --- Configuration ---
TIMEOUT=900
INTERVAL=60
START_TIME=$(date +%s)

echo "🚀 开始轮询流水线状态 (ID: ${PIPELINE_RUN_ID})..." >&2

# --- Main Polling Loop ---
while [ $(( $(date +%s) - START_TIME )) -lt $TIMEOUT ]; do
    STATUS_RESPONSE=$(curl -s -f --retry 3 --retry-delay 5 -X 'GET' "https://openapi-rdc.aliyuncs.com/oapi/v1/flow/organizations/${ORGANIZATION_ID}/pipelines/$PIPELINE_ID/runs/$PIPELINE_RUN_ID" \
        -H 'Content-Type: application/json' \
        -H "x-yunxiao-token: $API_TOKEN")

    # --- Status Check Logic (Robust Method) ---

    # 1. Check for merge conflict
    CONFLICT_INFO=$(echo "$STATUS_RESPONSE" | jq -r 'first((.stages // [] | .[]) as $stage | ($stage.stageInfo.jobs // [] | .[]) as $job | select(($job.actions // [] | .[] | .displayType) == "CONFLICT_MERGE") | "Merge Conflict in Stage [\($stage.name)], Job [\($job.name)]")')
    if [ -n "$CONFLICT_INFO" ]; then
        echo "$CONFLICT_INFO"
        exit 3
    fi

    # 2. Check for any other failed or canceled stages
    FAILED_INFO=$(echo "$STATUS_RESPONSE" | jq -r 'first((.stages // [] | .[]) as $stage | select($stage.stageInfo.status == "FAIL" or $stage.stageInfo.status == "CANCELED") | "Stage [\($stage.name)] has status [\($stage.stageInfo.status)]")')
    if [ -n "$FAILED_INFO" ]; then
        echo "$FAILED_INFO"
        exit 4
    fi

    # 3. Check for success
    if echo "$STATUS_RESPONSE" | jq -e '(.stages // [] | .[]) | select(.name == "部署") | .stageInfo.status | select(. == "SUCCESS")' > /dev/null; then
        echo "Deployment Succeeded"
        exit 0
    fi

    echo "⏳ 流水线仍在运行中，将在 ${INTERVAL} 秒后再次检查..." >&2
    sleep $INTERVAL
done

# --- Timeout Handling ---
echo "Timeout after ${TIMEOUT} seconds"
exit 2