#!/bin/bash

# 设置脚本在遇到错误时立即退出
set -e
# 设置管道中任何命令失败都会导致整个管道失败
set -o pipefail

# --- 全局变量和初始化 ---
# 获取配置文件路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
CONFIG_FILE="$PROJECT_ROOT/.claude/commands/pipeline_deployment_mcp.md"

# 帮助函数，用于从配置文件中提取ID
get_pipeline_id() {
    local env_name="$1"
    local var_name="$2"
    # 直接匹配环境名称和变量名，提取值
    local id=$(awk -F'=' -v env="$env_name" -v var="$var_name" \
        '$0 ~ env && $0 ~ var {gsub(/\"/, "", $2); print $2; exit}' "$CONFIG_FILE")
    # 验证提取的ID是否为纯数字
    if [[ "$id" =~ ^[0-9]+$ ]]; then
        echo "$id"
    else
        echo ""
    fi
}

echo "🚀 开始执行云效流水线部署前置准备..."

# --- 步骤一：检查并验证ALIYUN_API_TOKEN ---
echo "➡️ 步骤一：检查并验证ALIYUN_API_TOKEN..."
if [ -z "$ALIYUN_API_TOKEN" ]; then
    echo "❌ 错误：未配置ALIYUN_API_TOKEN环境变量。"
    echo "💡 请先配置ALIYUN_API_TOKEN环境变量，然后重新运行此脚本。"
    exit 1
fi

echo "🔍 正在调用云效API验证Token有效性..."
# 同时捕获API响应体和HTTP状态码
# -s 静默模式, -w '%{http_code}' 输出HTTP状态码
API_RESPONSE=$(curl -s -w "\n%{http_code}" 'https://openapi-rdc.aliyuncs.com/oapi/v1/platform/user' -H 'Content-Type: application/json' -H "x-yunxiao-token: $ALIYUN_API_TOKEN")
HTTP_STATUS=$(echo "$API_RESPONSE" | tail -n1)
# 使用 sed '$d' 替代 head -n-1 以兼容 macOS 和 Linux
USER_INFO=$(echo "$API_RESPONSE" | sed '$d')

# 根据HTTP状态码判断Token是否有效
if [ "$HTTP_STATUS" != "200" ]; then
    echo "❌ 错误：ALIYUN_API_TOKEN无效或网络错误。API返回状态码: $HTTP_STATUS"
    echo "错误详情: $(echo "$USER_INFO" | jq -r .message 2>/dev/null || echo "$USER_INFO")"
    exit 1
fi

USER_NAME=$(echo "$USER_INFO" | jq -r .name)
echo "✅ ALIYUN_API_TOKEN验证通过，欢迎您: $USER_NAME"
echo ""

# --- 步骤二：配置云效MCP服务器 ---
echo "➡️ 步骤二：配置云效MCP服务器..."
echo "正在配置云效MCP服务器..."
# 检查MCP服务器是否已存在
if claude mcp list 2>/dev/null | grep -q "yunxiao"; then
    echo "⚠️ MCP服务器 yunxiao 已存在，正在更新配置..."
    claude mcp remove yunxiao 2>/dev/null
    claude mcp add yunxiao --env YUNXIAO_ACCESS_TOKEN=$ALIYUN_API_TOKEN -- npx -y alibabacloud-devops-mcp-server
else
    echo "正在添加新的MCP服务器..."
    claude mcp add yunxiao --env YUNXIAO_ACCESS_TOKEN=$ALIYUN_API_TOKEN -- npx -y alibabacloud-devops-mcp-server
fi
echo "✅ 云效MCP服务器配置完成。"
echo ""

# --- 步骤三：检查本地流水线ID是否已配置 ---
echo "➡️ 步骤二：检查本地流水线ID是否已配置..."

# 从配置文件中提取已配置的流水线ID
DEV_PIPELINE_ID=$(get_pipeline_id "dev环境" "DEV_PIPELINE_ID")
DEV2_PIPELINE_ID=$(get_pipeline_id "dev2环境" "DEV2_PIPELINE_ID")

# 如果两个ID都已配置，则流程结束
if [ -n "$DEV_PIPELINE_ID" ] && [ -n "$DEV2_PIPELINE_ID" ]; then
    echo "✅ 流水线ID已在 $CONFIG_FILE 中配置完毕。"
    echo "   - DEV环境流水线ID: $DEV_PIPELINE_ID"
    echo "   - DEV2环境流水线ID: $DEV2_PIPELINE_ID"
    echo "🎉 前置准备工作已完成，无需再次执行。脚本正常退出。"
    exit 0
else
    echo "⚠️ 检测到流水线ID未完全配置，将继续执行以帮助您完成配置。"

    # 提供更详细的配置指导
    if [ -z "$DEV_PIPELINE_ID" ]; then
        echo "   ❗ DEV环境流水线ID未配置"
    fi
    if [ -z "$DEV2_PIPELINE_ID" ]; then
        echo "   ❗ DEV2环境流水线ID未配置"
    fi
fi
echo ""

# --- 步骤三：获取代码库名称 ---
echo "➡️ 步骤三：获取代码库名称..."

# 检查是否在Git仓库中
if ! git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
    echo "❌ 错误：当前目录不是一个Git仓库，无法自动获取代码库名称。"
    exit 1
fi

REPO_URL=$(git config --get remote.origin.url)
if [[ "$REPO_URL" == git@* ]]; then
    REPO_NAME=$(echo "$REPO_URL" | sed 's/.*:\(.*\)\.git/\1/' | awk -F'/' '{print $NF}')
else
    REPO_NAME=$(echo "$REPO_URL" | sed 's/.*\/\(.*\)\.git/\1/')
fi
echo "✅ 获取到代码库名称: $REPO_NAME"
echo ""

# --- 步骤四：查询并列出可用的流水线 ---
echo "➡️ 步骤四：查询并列出可用的流水线..."
echo "🔍 正在查询与代码库[$REPO_NAME]相关的流水线列表..."

# 调用云效API查询流水线列表
PIPELINES_RESPONSE=$(curl -s "https://openapi-rdc.aliyuncs.com/oapi/v1/flow/organizations/6189f099041d450d2c253abc/pipelines?pipelineName=$REPO_NAME&perPage=100&page=1" \
  -H 'Content-Type: application/json' \
  -H "x-yunxiao-token: $ALIYUN_API_TOKEN")

# 检查API返回是否为空或有误
if [ -z "$PIPELINES_RESPONSE" ] || ! echo "$PIPELINES_RESPONSE" | jq empty 2>/dev/null; then
    echo "❌ 错误：查询流水线列表失败，API无返回或返回格式错误。"
    exit 1
fi

PIPELINE_COUNT=$(echo "$PIPELINES_RESPONSE" | jq 'length')
if [ "$PIPELINE_COUNT" -eq 0 ]; then
    echo "🟡 警告：未找到与代码库名称[$REPO_NAME]匹配的流水线。"
    echo "💡 请检查云效中的流水线名称是否与代码库名称一致。"
    exit 1
fi

# 解析并排序打印流水线信息
echo ""
echo "📋 以下是为您找到的相关流水线列表："
echo "流水线ID | 名称"
echo "---------- | ----"
# 使用jq内置的sort_by进行排序，以确保在所有平台上的行为一致
echo "$PIPELINES_RESPONSE" | jq -r '. | sort_by(.name) | .[] | "\(.id) | \(.name)"'
echo ""

# --- 最终引导 ---
echo ""
    echo "📋 配置指南："
    echo "1. 打开文件: $CONFIG_FILE"
    echo "2. 找到 '声明流水线的PIPELINE_ID' 部分"
    echo "3. 在对应环境的行中填入正确的流水线ID，格式如下："
    echo "   dev环境: DEV_PIPELINE_ID=123456"
    echo "   dev2环境: DEV2_PIPELINE_ID=456789"
    echo "4. 保存文件后重新运行此脚本"
    echo ""
    echo "💡 请从上面的列表中找到您需要的流水线ID，并将其配置到以下文件中："
#echo "💡 请从上面的列表中找到您需要的流水线ID，并将其配置到以下文件中："
echo "   $CONFIG_FILE"
echo ""
echo "✅ 前置准备完成！"