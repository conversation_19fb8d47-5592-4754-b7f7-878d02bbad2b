@echo off
setlocal enabledelayedexpansion

echo 🚀 开始执行云效流水线部署前置准备...

:: --- 步骤一：检查并验证ALIYUN_API_TOKEN ---
echo ➡️ 步骤一：检查并验证ALIYUN_API_TOKEN...
if "!ALIYUN_API_TOKEN!" == "" (
    echo ❌ 错误：未配置ALIYUN_API_TOKEN环境变量。
    echo 💡 请先配置ALIYUN_API_TOKEN环境变量，然后重新运行此脚本。
    exit /b 1
)

echo 🔍 正在调用云效API验证Token有效性...
:: 使用 PowerShell 调用 curl 并获取响应
for /f "delims=" %%i in ('powershell -Command "& { $response = Invoke-WebRequest -Uri 'https://openapi-rdc.aliyuncs.com/oapi/v1/platform/user' -Headers @{ 'Content-Type' = 'application/json'; 'x-yunxiao-token' = '%ALIYUN_API_TOKEN%' } -ErrorAction Stop; $response.Content }"') do set "USER_INFO=%%i"

:: 检查 PowerShell 命令是否成功执行
if errorlevel 1 (
    echo ❌ 错误：ALIYUN_API_TOKEN无效或网络错误。
    exit /b 1
)

:: 使用 PowerShell 解析 JSON 获取用户名
for /f "delims=" %%i in ('powershell -Command "& { $userInfo = %USER_INFO%; $json = ConvertFrom-Json -InputObject $userInfo; $json.name }"') do set "USER_NAME=%%i"
echo ✅ ALIYUN_API_TOKEN验证通过，欢迎您: !USER_NAME!
echo.

:: --- 步骤二：配置云效MCP服务器 ---
echo ➡️ 步骤二：配置云效MCP服务器...
echo 正在配置云效MCP服务器...
:: 检查MCP服务器是否已存在
for /f "delims=" %%i in ('claude mcp list 2^>nul ^| findstr "yunxiao"') do set "MCP_EXISTS=1"
if defined MCP_EXISTS (
    echo ⚠️ MCP服务器 yunxiao 已存在，正在更新配置...
    claude mcp remove yunxiao 2>nul
    claude mcp add yunxiao --env YUNXIAO_ACCESS_TOKEN=!ALIYUN_API_TOKEN! -- npx -y alibabacloud-devops-mcp-server
) else (
    echo 正在添加新的MCP服务器...
    claude mcp add yunxiao --env YUNXIAO_ACCESS_TOKEN=!ALIYUN_API_TOKEN! -- npx -y alibabacloud-devops-mcp-server
)
echo ✅ 云效MCP服务器配置完成。
echo.

:: --- 步骤三：检查本地流水线ID是否已配置 ---
echo ➡️ 步骤二：检查本地流水线ID是否已配置...

:: 读取配置文件并提取流水线ID
set "CONFIG_FILE=%~dp0..\..\commands\pipeline_deployment_mcp.md"
set "DEV_PIPELINE_ID="
set "DEV2_PIPELINE_ID="

for /f "tokens=2 delims==" %%i in ('findstr /C:"dev环境: DEV_PIPELINE_ID=" "%CONFIG_FILE%"') do set "DEV_PIPELINE_ID=%%i"
for /f "tokens=2 delims==" %%i in ('findstr /C:"dev2环境: DEV2_PIPELINE_ID=" "%CONFIG_FILE%"') do set "DEV2_PIPELINE_ID=%%i"

:: 如果两个ID都已配置，则流程结束
if defined DEV_PIPELINE_ID if defined DEV2_PIPELINE_ID (
    echo ✅ 流水线ID已在 !CONFIG_FILE! 中配置完毕。
    echo    - DEV环境流水线ID: !DEV_PIPELINE_ID!
    echo    - DEV2环境流水线ID: !DEV2_PIPELINE_ID!
    echo 🎉 前置准备工作已完成，无需再次执行。脚本正常退出。
    exit /b 0
) else (
    echo ⚠️ 检测到流水线ID未完全配置，将继续执行以帮助您完成配置。
    if not defined DEV_PIPELINE_ID echo    ❗ DEV环境流水线ID未配置
    if not defined DEV2_PIPELINE_ID echo    ❗ DEV2环境流水线ID未配置
)
echo.

:: --- 步骤三：获取代码库名称 ---
echo ➡️ 步骤三：获取代码库名称...

:: 检查是否在Git仓库中
git rev-parse --is-inside-work-tree >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：当前目录不是一个Git仓库，无法自动获取代码库名称。
    exit /b 1
)

:: 获取仓库URL并提取仓库名称
for /f "delims=" %%i in ('git config --get remote.origin.url') do set "REPO_URL=%%i"

:: 处理不同的URL格式
set "REPO_NAME=!REPO_URL!"
if "!REPO_URL:~0,4!" == "git@" (
    for /f "tokens=2 delims=/" %%i in ("!REPO_URL:git@=!" ) do set "temp=%%i"
    for /f "delims=." %%i in ("!temp!" ) do set "REPO_NAME=%%i"
) else (
    for /f "tokens=2 delims=/" %%i in ("!REPO_URL:https://=!" ) do set "temp=%%i"
    for /f "delims=." %%i in ("!temp!" ) do set "REPO_NAME=%%i"
)

echo ✅ 获取到代码库名称: !REPO_NAME!
echo.

:: --- 步骤四：查询并列出可用的流水线 ---
echo ➡️ 步骤四：查询并列出可用的流水线...
echo 🔍 正在查询与代码库[!REPO_NAME!]相关的流水线列表...

:: 调用云效API查询流水线列表
for /f "delims=" %%i in ('powershell -Command "& { $response = Invoke-WebRequest -Uri 'https://openapi-rdc.aliyuncs.com/oapi/v1/flow/organizations/6189f099041d450d2c253abc/pipelines?pipelineName=!REPO_NAME!&perPage=100&page=1' -Headers @{ 'Content-Type' = 'application/json'; 'x-yunxiao-token' = '%ALIYUN_API_TOKEN%' } -ErrorAction Stop; $response.Content }"') do set "PIPELINES_RESPONSE=%%i"

:: 检查API返回是否为空或有误
if "!PIPELINES_RESPONSE!" == "" (
    echo ❌ 错误：查询流水线列表失败，API无返回或返回格式错误。
    exit /b 1
)

:: 使用 PowerShell 解析 JSON 获取流水线数量
for /f "delims=" %%i in ('powershell -Command "& { $pipelines = !PIPELINES_RESPONSE!; $json = ConvertFrom-Json -InputObject $pipelines; $json.Count }"') do set "PIPELINE_COUNT=%%i"

if "!PIPELINE_COUNT!" == "0" (
    echo 🟡 警告：未找到与代码库名称[!REPO_NAME!]匹配的流水线。
    echo 💡 请检查云效中的流水线名称是否与代码库名称一致。
    exit /b 1
)

:: 解析并打印流水线信息
echo.
echo 📋 以下是为您找到的相关流水线列表：
echo 流水线ID ^| 名称
echo ---------- ^| ----

:: 使用 PowerShell 解析 JSON 并排序打印流水线信息
powershell -Command "& { $pipelines = !PIPELINES_RESPONSE!; $json = ConvertFrom-Json -InputObject $pipelines; $json | Sort-Object name | ForEach-Object { Write-Host ($_.id + ' | ' + $_.name) } }"
echo.

:: --- 最终引导 ---
echo.
echo 📋 配置指南：
echo 1. 打开文件: !CONFIG_FILE!
echo 2. 找到 '声明流水线的PIPELINE_ID' 部分
echo 3. 在对应环境的行中填入正确的流水线ID，格式如下：
echo    dev环境: DEV_PIPELINE_ID=123456
echo    dev2环境: DEV2_PIPELINE_ID=456789
echo 4. 保存文件后重新运行此脚本
echo.
echo 💡 请从上面的列表中找到您需要的流水线ID，并将其配置到以下文件中：
echo    !CONFIG_FILE!
echo.
echo ✅ 前置准备完成！