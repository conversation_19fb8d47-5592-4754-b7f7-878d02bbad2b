{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(mvn:*)", "Read(*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(tree:*)", "Bash(git:*)", "Bash(java:*)", "Write(*)", "Edit(*)", "MultiEdit(*)", "Bash(if:*)", "Bash(git:*)", "<PERSON><PERSON>(curl:*)", "Bash(if:*)", "<PERSON><PERSON>(then:*)", "Bash(else:*)", "Bash(fi:*)", "Ba<PERSON>([:*)", "Bash(.claude/scripts/*)", "Bash(./.claude/scripts/poll_pipeline_status.sh:*)", "mcp__yun<PERSON>o", "Bash(.claude/scripts/poll_pipeline_status.sh:*)", "SlashCommand(/pipeline_deployment_mcp)"], "deny": ["Ba<PERSON>(mvn deploy:*)"], "ask": ["<PERSON><PERSON>(chmod:*)", "Bash(rm:*)", "Bash(xargs:*)", "<PERSON><PERSON>(sleep:*)"]}}