# Claude Code 命令使用和开发指南

## 概述

Claude Code 命令是基于 Markdown 文件定义的自定义命令，位于 `claude/commands/xm/` 目录下。每个命令由一个 Markdown 文件定义，文件名即为命令名。

## 命令使用方法

### 运行命令

在 Claude Code 中，可以通过以下方式运行命令：

1. 使用CLI工具比如WARP或者直接在命令行中运行：`ccr code /命令名`
2. 直接在聊天中输入：`/xm:命令名`（例如 `/xm:cr-java`）

### 命令参数

某些命令支持参数，可以在命令后添加参数：
```
/xm:命令名 参数
```

## 命令开发规范

### 文件结构

每个命令由一个 Markdown 文件定义，文件应包含以下部分：

#### Frontmatter（前置配置）

文件顶部使用 YAML 格式的前置配置，定义命令的基本信息：

```yaml
---
allowed-tools: 允许使用的工具列表
description: 命令描述
---
```

- `allowed-tools`：定义命令可以使用的工具，如 `Bash(git:*)`、`Read(*)` 等
- `description`：命令的简要描述

#### 角色定义

使用 Markdown 标题定义命令的角色和职责：
```markdown
# 角色定义

描述命令的角色和任务
```

#### 核心指令

定义命令必须遵循的核心指令：
```markdown
## 核心指令

列出核心指令和约束条件
```

#### 执行流程

详细描述命令的执行步骤：
```markdown
## 执行工作流程

按步骤描述命令的执行流程
```

### 工具使用规范

1. **文件读取**：使用 `Read` 工具读取文件内容
2. **文件写入**：使用 `Write` 或 `Edit` 工具修改文件
3. **命令执行**：使用 `Bash` 工具执行系统命令
4. **文件搜索**：使用 `Glob` 工具进行文件模式匹配

### 命令示例

以下是一个简单的命令示例：

```markdown
---
allowed-tools: Bash(ls:*), Read(*), Write(*.md)
description: 列出项目文件并生成报告
---

# 角色定义

您是一位项目分析专家，负责分析项目结构并生成报告。

## 核心指令

1. 必须使用 Read 工具读取文件
2. 必须使用 Write 工具创建报告文件

## 执行工作流程

### 步骤 1：获取项目结构

使用以下命令获取项目文件列表：
```bash
ls -la
```

### 步骤 2：生成报告

将项目结构信息写入 `project_report.md` 文件。
```

## 现有命令说明

### /xm:cr-java - Java代码审查命令

用于对比当前分支和主分支的代码变更，进行Java代码审查。

主要功能：
- 自动获取最新代码
- 分析代码变更差异
- 生成详细的代码审查报告

### /xm:cr-python - Python代码审查命令

用于对比当前分支和主分支的代码变更，进行Python代码审查。

主要功能：
- 自动获取最新代码
- 分析代码变更差异
- 生成详细的代码审查报告

### /xm:gitcommit - Git提交命令

自动生成中文Git提交信息并推送到远程仓库。

主要功能：
- 分析暂存区和未暂存的更改
- 自动生成符合规范的中文提交信息
- 自动提交并推送到远程仓库

### /xm:junit-java - JUnit5测试生成命令

根据现有代码自动生成JUnit5单元测试。

主要功能：
- 分析被测代码结构
- 生成符合JUnit5规范的测试用例
- 提供测试运行指导

## 开发建议

1. **保持专注**：每个命令应专注于单一功能
2. **错误处理**：考虑各种异常情况并提供适当的错误处理
3. **用户友好**：提供清晰的说明和反馈
4. **安全性**：避免执行危险的系统命令
5. **可维护性**：使用清晰的结构和注释