#!/bin/bash

echo "=== 合并请求代码审查工具安装脚本 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1)
if [[ $? -eq 0 ]]; then
    echo "✓ 检测到Python: $python_version"
else
    echo "✗ 未检测到Python3，请先安装Python3"
    exit 1
fi

# 检查pip
pip_version=$(pip3 --version 2>&1)
if [[ $? -eq 0 ]]; then
    echo "✓ 检测到pip: $pip_version"
else
    echo "✗ 未检测到pip3，请先安装pip3"
    exit 1
fi

# 安装依赖
echo ""
echo "正在安装Python依赖包..."
pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

if [[ $? -eq 0 ]]; then
    echo "✓ 依赖包安装成功"
else
    echo "✗ 依赖包安装失败"
    exit 1
fi

# 检查环境变量
echo ""
echo "检查环境变量配置..."

# 检查阿里云访问密钥
ALIBABA_KEY_MISSING=false
if [[ -z "$ALIBABA_CLOUD_ACCESS_KEY_ID" ]]; then
    echo "⚠ 未设置 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量"
    ALIBABA_KEY_MISSING=true
else
    echo "✓ ALIBABA_CLOUD_ACCESS_KEY_ID 已设置"
fi

if [[ -z "$ALIBABA_CLOUD_ACCESS_KEY_SECRET" ]]; then
    echo "⚠ 未设置 ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量"
    ALIBABA_KEY_MISSING=true
else
    echo "✓ ALIBABA_CLOUD_ACCESS_KEY_SECRET 已设置"
fi

# 如果阿里云密钥缺失，提示用户输入
if [[ "$ALIBABA_KEY_MISSING" == "true" ]]; then
    echo ""
    echo "=== 配置阿里云访问密钥 ==="
    echo "为了使用云效API，需要配置阿里云访问密钥。"
    echo "您可以在阿里云控制台 -> AccessKey管理 中获取这些信息。"
    echo ""
    
    # 获取用户输入
    if [[ -z "$ALIBABA_CLOUD_ACCESS_KEY_ID" ]]; then
        read -p "请输入 ALIBABA_CLOUD_ACCESS_KEY_ID: " ACCESS_KEY_ID
        if [[ -z "$ACCESS_KEY_ID" ]]; then
            echo "✗ ACCESS_KEY_ID 不能为空"
            exit 1
        fi
    else
        ACCESS_KEY_ID="$ALIBABA_CLOUD_ACCESS_KEY_ID"
    fi
    
    if [[ -z "$ALIBABA_CLOUD_ACCESS_KEY_SECRET" ]]; then
        read -s -p "请输入 ALIBABA_CLOUD_ACCESS_KEY_SECRET: " ACCESS_KEY_SECRET
        echo ""  # 换行
        if [[ -z "$ACCESS_KEY_SECRET" ]]; then
            echo "✗ ACCESS_KEY_SECRET 不能为空"
            exit 1
        fi
    else
        ACCESS_KEY_SECRET="$ALIBABA_CLOUD_ACCESS_KEY_SECRET"
    fi
    
    # 确定要写入的配置文件
    SHELL_TYPE=$(basename "$SHELL")
    PROFILE_FILE=""
    
    if [ "$SHELL_TYPE" = "zsh" ]; then
        PROFILE_FILE="$HOME/.zshrc"
    elif [ "$SHELL_TYPE" = "bash" ]; then
        if [ -f "$HOME/.bash_profile" ]; then
            PROFILE_FILE="$HOME/.bash_profile"
        else
            PROFILE_FILE="$HOME/.bashrc"
        fi
    else
        # 默认尝试 .zshrc，因为macOS默认使用zsh
        PROFILE_FILE="$HOME/.zshrc"
    fi
    
    # 写入环境变量到配置文件
    echo ""
    echo "正在将阿里云访问密钥写入 $PROFILE_FILE ..."
    
    # 检查是否已存在相关配置
    if grep -q "ALIBABA_CLOUD_ACCESS_KEY" "$PROFILE_FILE" 2>/dev/null; then
        echo "⚠ 检测到配置文件中已存在阿里云密钥配置，请手动检查和更新。"
    else
        echo "" >> "$PROFILE_FILE"
        echo "# 阿里云访问密钥 (由mr-code-review/setup.sh添加)" >> "$PROFILE_FILE"
        echo "export ALIBABA_CLOUD_ACCESS_KEY_ID=\"$ACCESS_KEY_ID\"" >> "$PROFILE_FILE"
        echo "export ALIBABA_CLOUD_ACCESS_KEY_SECRET=\"$ACCESS_KEY_SECRET\"" >> "$PROFILE_FILE"
        echo "✓ 阿里云访问密钥已写入配置文件"
        echo "请重启终端或执行 'source $PROFILE_FILE' 使配置生效。"
    fi
fi

# 检查LLM API密钥
LLM_KEY_MISSING=false
if [[ -z "$XM_LLM_API_KEY" ]]; then
    echo "⚠ 未设置 XM_LLM_API_KEY 环境变量"
    LLM_KEY_MISSING=true
else
    echo "✓ XM_LLM_API_KEY 已设置"
fi

# 如果LLM API密钥缺失，提示用户输入
if [[ "$LLM_KEY_MISSING" == "true" ]]; then
    echo ""
    echo "=== 配置LLM API密钥 ==="
    echo "为了使用AI代码审查功能，需要配置LLM API密钥。"
    echo "您可以访问 https://litellm-test.summerfarm.net/v1 获取API_KEY。"
    echo ""
    
    read -p "请输入 XM_LLM_API_KEY: " LLM_API_KEY
    if [[ -z "$LLM_API_KEY" ]]; then
        echo "✗ XM_LLM_API_KEY 不能为空"
        exit 1
    fi
    
    # 确定要写入的配置文件
    SHELL_TYPE=$(basename "$SHELL")
    PROFILE_FILE=""
    
    if [ "$SHELL_TYPE" = "zsh" ]; then
        PROFILE_FILE="$HOME/.zshrc"
    elif [ "$SHELL_TYPE" = "bash" ]; then
        if [ -f "$HOME/.bash_profile" ]; then
            PROFILE_FILE="$HOME/.bash_profile"
        else
            PROFILE_FILE="$HOME/.bashrc"
        fi
    else
        # 默认尝试 .zshrc，因为macOS默认使用zsh
        PROFILE_FILE="$HOME/.zshrc"
    fi
    
    # 写入环境变量到配置文件
    echo ""
    echo "正在将LLM API密钥写入 $PROFILE_FILE ..."
    
    # 检查是否已存在相关配置
    if grep -q "XM_LLM_API_KEY" "$PROFILE_FILE" 2>/dev/null; then
        echo "⚠ 检测到配置文件中已存在LLM API密钥配置，请手动检查和更新。"
    else
        echo "" >> "$PROFILE_FILE"
        echo "# LLM API密钥 (由mr-code-review/setup.sh添加)" >> "$PROFILE_FILE"
        echo "export XM_LLM_API_KEY=\"$LLM_API_KEY\"" >> "$PROFILE_FILE"
        echo "✓ LLM API密钥已写入配置文件"
        echo "请重启终端或执行 'source $PROFILE_FILE' 使配置生效。"
    fi
fi

# 检查其他可选环境变量

if [[ -z "$XM_LLM_BASE_URL" ]]; then
    echo "⚠ 未设置 XM_LLM_BASE_URL 环境变量，将使用默认值"
else
    echo "✓ XM_LLM_BASE_URL 已设置"
fi

if [[ -z "$XM_LLM_MODEL" ]]; then
    echo "⚠ 未设置 XM_LLM_MODEL 环境变量，将使用默认值"
else
    echo "✓ XM_LLM_MODEL 已设置"
fi

echo ""
echo "=== 配置快捷命令 ==="

# 获取脚本所在目录的绝对路径
# BASH_SOURCE在zsh下可能不兼容，所以换成$0，更具兼容性
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
ALIAS_COMMAND="alias cr='python3 ${SCRIPT_DIR}/mr_code_review.py --url '"

# 检测用户的shell
SHELL_TYPE=$(basename "$SHELL")
CONFIG_FILE=""

if [ "$SHELL_TYPE" = "zsh" ]; then
    CONFIG_FILE="$HOME/.zshrc"
elif [ "$SHELL_TYPE" = "bash" ]; then
    # 在macOS上，bash默认是交互式登录shell，会加载 .bash_profile
    if [ -f "$HOME/.bash_profile" ]; then
        CONFIG_FILE="$HOME/.bash_profile"
    else
        CONFIG_FILE="$HOME/.bashrc"
    fi
else
    echo "⚠ 无法自动检测到您的shell类型 (当前是 $SHELL_TYPE), 请手动配置别名。"
    echo "请将以下内容添加到您的shell配置文件中:"
    echo "  $ALIAS_COMMAND"
fi

if [ -n "$CONFIG_FILE" ]; then
    # 检查别名是否已存在，避免重复添加
    if grep -q "alias cr='python3 ${SCRIPT_DIR}/mr_code_review.py --url'" "$CONFIG_FILE" 2>/dev/null; then
        echo "✓ 快捷命令 'cr' 已经存在于 $CONFIG_FILE"
    else
        echo "正在将快捷命令 'cr' 添加到 $CONFIG_FILE ..."
        echo "" >> "$CONFIG_FILE"
        echo "# Code Review 快捷命令 (由mr-code-review/setup.sh添加)" >> "$CONFIG_FILE"
        echo "$ALIAS_COMMAND" >> "$CONFIG_FILE"
        echo "✓ 快捷命令 'cr' 添加成功。"
        echo "请重启您的终端，或执行 'source $CONFIG_FILE' 来使命令生效。"
    fi
fi

echo ""
echo "=== 安装完成 ==="
echo ""
echo "使用方法："
echo "  cr <URL>"

echo ""
echo "示例："
echo "  cr https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"
echo ""
echo "如果 'cr' 命令无效，你也可以使用完整命令："
echo "  python3 mr_code_review.py <URL>"
echo ""
echo "环境变量配置："
echo "  # 阿里云访问密钥（必需）"
echo "  export ALIBABA_CLOUD_ACCESS_KEY_ID=\"your-access-key-id\""
echo "  export ALIBABA_CLOUD_ACCESS_KEY_SECRET=\"your-access-key-secret\""
echo ""
echo "  # LLM API密钥（必需）"
echo "  export XM_LLM_API_KEY=\"your-api-key\""
echo ""
echo "  # LLM配置（可选）"
echo "  export XM_LLM_BASE_URL=\"https://litellm-test.summerfarm.net/v1\""
echo "  export XM_LLM_MODEL=\"deepseek-v3-250324\""
