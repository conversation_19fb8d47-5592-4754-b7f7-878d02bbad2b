@echo off
setlocal enabledelayedexpansion

echo 🚀 开始安装 Claude Code 和 Claude Code Router...

REM 检查 Node.js 和 npm 是否已安装
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm 未安装，请先安装 Node.js 和 npm
    pause
    exit /b 1
)

echo 📦 正在安装 Claude Code...
REM 安装 Claude Code
npm install -g @anthropic-ai/claude-code --registry=https://registry.npmmirror.com

if !errorlevel! equ 0 (
    echo ✅ Claude Code 安装成功
) else (
    echo ❌ Claude Code 安装失败
    pause
    exit /b 1
)

echo 📦 正在安装 Claude Code Router...
REM 安装 Claude Code Router
npm install -g @musistudio/claude-code-router --registry=https://registry.npmmirror.com

if !errorlevel! equ 0 (
    echo ✅ Claude Code Router 安装成功
) else (
    echo ❌ Claude Code Router 安装失败
    pause
    exit /b 1
)

echo 📁 正在配置 Claude Code 命令...

REM 创建 %USERPROFILE%\.claude\commands 目录
set CLAUDE_COMMANDS_DIR=%USERPROFILE%\.claude\commands
if not exist "%CLAUDE_COMMANDS_DIR%" mkdir "%CLAUDE_COMMANDS_DIR%"

REM 复制 xm 文件夹到用户的 %USERPROFILE%\.claude\commands 目录
if exist "claude\commands\xm" (
    xcopy /E /I /Y "claude\commands\xm" "%CLAUDE_COMMANDS_DIR%\xm"
    echo ✅ 已复制 xm 命令到 %CLAUDE_COMMANDS_DIR%\xm
) else (
    echo ⚠️  警告: claude\commands\xm 目录不存在，跳过复制
)

echo 📁 正在配置 Claude Code Router...

REM 创建 %USERPROFILE%\.claude-code-router 目录
set CLAUDE_ROUTER_DIR=%USERPROFILE%\.claude-code-router
if not exist "%CLAUDE_ROUTER_DIR%" mkdir "%CLAUDE_ROUTER_DIR%"

REM 复制 config.json 到 %USERPROFILE%\.claude-code-router\config.json
if exist "claude\claude-code-router\config.json" (
    copy /Y "claude\claude-code-router\config.json" "%CLAUDE_ROUTER_DIR%\config.json"
    echo ✅ 已复制配置文件到 %CLAUDE_ROUTER_DIR%\config.json
) else (
    echo ⚠️  警告: claude\claude-code-router\config.json 文件不存在，跳过复制
)

echo.
echo 🎉 Claude Code 和 Claude Code Router 安装配置完成！
echo.
echo 📋 安装的组件：
echo    • Claude Code (全局安装)
echo    • Claude Code Router (全局安装)
echo    • xm 命令集 (位于 %USERPROFILE%\.claude\commands\xm)
echo    • Router 配置文件 (位于 %USERPROFILE%\.claude-code-router\config.json)
echo.
echo 🔧 使用方法：
echo    • 运行 'claude-code' 启动 Claude Code
echo    • 运行 'claude-code-router' 启动 Claude Code Router
echo.
echo 💡 提示：
echo    • 如需修改配置，请编辑 %USERPROFILE%\.claude-code-router\config.json
echo    • 如需添加更多命令，请在 %USERPROFILE%\.claude\commands 目录下添加
echo.

pause