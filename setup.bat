@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === 合并请求代码审查工具安装脚本 ===
echo.

:: 检查Python版本
echo 正在检查Python环境...
python --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do set python_version=%%i
    echo ✓ 检测到Python: !python_version!
) else (
    echo ✗ 未检测到Python，请先安装Python
    echo.
    echo 请访问 https://www.python.org/downloads/ 下载并安装Python
    pause
    exit /b 1
)

:: 检查pip
echo 正在检查pip...
pip --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=*" %%i in ('pip --version 2^>^&1') do set pip_version=%%i
    echo ✓ 检测到pip: !pip_version!
) else (
    echo ✗ 未检测到pip，请确保Python安装正确
    pause
    exit /b 1
)

:: 安装依赖
echo.
echo 正在安装Python依赖包...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
if !errorlevel! equ 0 (
    echo ✓ 依赖包安装成功
) else (
    echo ✗ 依赖包安装失败
    pause
    exit /b 1
)

:: 检查环境变量
echo.
echo 检查环境变量配置...

:: 检查阿里云访问密钥
set "ALIBABA_KEY_MISSING=false"
if "%ALIBABA_CLOUD_ACCESS_KEY_ID%"=="" (
    echo ⚠ 未设置 ALIBABA_CLOUD_ACCESS_KEY_ID 环境变量
    set "ALIBABA_KEY_MISSING=true"
) else (
    echo ✓ ALIBABA_CLOUD_ACCESS_KEY_ID 已设置
)

if "%ALIBABA_CLOUD_ACCESS_KEY_SECRET%"=="" (
    echo ⚠ 未设置 ALIBABA_CLOUD_ACCESS_KEY_SECRET 环境变量
    set "ALIBABA_KEY_MISSING=true"
) else (
    echo ✓ ALIBABA_CLOUD_ACCESS_KEY_SECRET 已设置
)

:: 如果阿里云密钥缺失，提示用户输入
if "!ALIBABA_KEY_MISSING!"=="true" (
    echo.
    echo === 配置阿里云访问密钥 ===
    echo 为了使用云效API，需要配置阿里云访问密钥。
    echo 您可以在阿里云控制台 -^> AccessKey管理 中获取这些信息。
    echo.
    
    :: 获取用户输入
    if "%ALIBABA_CLOUD_ACCESS_KEY_ID%"=="" (
        set /p "ACCESS_KEY_ID=请输入 ALIBABA_CLOUD_ACCESS_KEY_ID: "
        if "!ACCESS_KEY_ID!"=="" (
            echo ✗ ACCESS_KEY_ID 不能为空
            pause
            exit /b 1
        )
    ) else (
        set "ACCESS_KEY_ID=%ALIBABA_CLOUD_ACCESS_KEY_ID%"
    )
    
    if "%ALIBABA_CLOUD_ACCESS_KEY_SECRET%"=="" (
        set /p "ACCESS_KEY_SECRET=请输入 ALIBABA_CLOUD_ACCESS_KEY_SECRET: "
        if "!ACCESS_KEY_SECRET!"=="" (
            echo ✗ ACCESS_KEY_SECRET 不能为空
            pause
            exit /b 1
        )
    ) else (
        set "ACCESS_KEY_SECRET=%ALIBABA_CLOUD_ACCESS_KEY_SECRET%"
    )
    
    :: 设置用户环境变量
    echo.
    echo 正在设置阿里云访问密钥到用户环境变量...
    
    :: 使用setx命令永久设置用户环境变量
    setx ALIBABA_CLOUD_ACCESS_KEY_ID "!ACCESS_KEY_ID!" >nul
    if !errorlevel! equ 0 (
        echo ✓ ALIBABA_CLOUD_ACCESS_KEY_ID 设置成功
    ) else (
        echo ✗ ALIBABA_CLOUD_ACCESS_KEY_ID 设置失败
    )
    
    setx ALIBABA_CLOUD_ACCESS_KEY_SECRET "!ACCESS_KEY_SECRET!" >nul
    if !errorlevel! equ 0 (
        echo ✓ ALIBABA_CLOUD_ACCESS_KEY_SECRET 设置成功
    ) else (
        echo ✗ ALIBABA_CLOUD_ACCESS_KEY_SECRET 设置失败
    )
    
    echo.
    echo ✓ 阿里云访问密钥已设置到用户环境变量
    echo 请重启命令提示符或PowerShell使环境变量生效。
)

:: 检查LLM API密钥
set "LLM_KEY_MISSING=false"
if "%XM_LLM_API_KEY%"=="" (
    echo ⚠ 未设置 XM_LLM_API_KEY 环境变量
    set "LLM_KEY_MISSING=true"
) else (
    echo ✓ XM_LLM_API_KEY 已设置
)

:: 如果LLM API密钥缺失，提示用户输入
if "!LLM_KEY_MISSING!"=="true" (
    echo.
    echo === 配置LLM API密钥 ===
    echo 为了使用AI代码审查功能，需要配置LLM API密钥。
    echo 您可以访问 https://litellm-test.summerfarm.net/v1 获取API_KEY。
    echo.
    
    set /p "LLM_API_KEY=请输入 XM_LLM_API_KEY: "
    if "!LLM_API_KEY!"=="" (
        echo ✗ XM_LLM_API_KEY 不能为空
        pause
        exit /b 1
    )
    
    :: 设置用户环境变量
    echo.
    echo 正在设置LLM API密钥到用户环境变量...
    
    :: 使用setx命令永久设置用户环境变量
    setx XM_LLM_API_KEY "!LLM_API_KEY!" >nul
    if !errorlevel! equ 0 (
        echo ✓ XM_LLM_API_KEY 设置成功
    ) else (
        echo ✗ XM_LLM_API_KEY 设置失败
    )
    
    echo.
    echo ✓ LLM API密钥已设置到用户环境变量
    echo 请重启命令提示符或PowerShell使环境变量生效。
)

:: 检查其他可选环境变量

if "%XM_LLM_BASE_URL%"=="" (
    echo ⚠ 未设置 XM_LLM_BASE_URL 环境变量，将使用默认值
) else (
    echo ✓ XM_LLM_BASE_URL 已设置
)

if "%XM_LLM_MODEL%"=="" (
    echo ⚠ 未设置 XM_LLM_MODEL 环境变量，将使用默认值
) else (
    echo ✓ XM_LLM_MODEL 已设置
)

echo.
echo === 配置快捷命令 ===

:: 获取脚本所在目录的绝对路径
set "SCRIPT_DIR=%~dp0"
:: 移除末尾的反斜杠
if "!SCRIPT_DIR:~-1!"=="\" set "SCRIPT_DIR=!SCRIPT_DIR:~0,-1!"

:: 创建批处理文件用于快捷命令
set "CR_BAT_PATH=!SCRIPT_DIR!\cr.bat"
echo @echo off > "!CR_BAT_PATH!"
echo python "!SCRIPT_DIR!\mr_code_review.py" --url %%* >> "!CR_BAT_PATH!"

:: 检查PATH中是否已包含脚本目录
echo !PATH! | findstr /i "!SCRIPT_DIR!" >nul
if !errorlevel! equ 0 (
    echo ✓ 快捷命令 'cr' 路径已在系统PATH中
) else (
    echo ⚠ 脚本目录未在系统PATH中，请手动将以下路径添加到系统PATH环境变量：
    echo   !SCRIPT_DIR!
    echo   或者使用完整路径调用：!CR_BAT_PATH!
)

echo ✓ 快捷命令 'cr.bat' 创建成功

echo.
echo === 安装完成 ===
echo.
echo 使用方法：
echo   cr ^<URL^>
echo.
echo 示例：
echo   cr https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs
echo.
echo 如果 'cr' 命令无效，你也可以使用完整命令：
echo   python mr_code_review.py ^<URL^>
echo.
echo 环境变量配置：
echo   # 阿里云访问密钥（必需）
echo   set ALIBABA_CLOUD_ACCESS_KEY_ID="your-access-key-id"
echo   set ALIBABA_CLOUD_ACCESS_KEY_SECRET="your-access-key-secret"
echo.
echo   # LLM API密钥（必需）
echo   set XM_LLM_API_KEY="your-api-key"
echo.
echo   # LLM配置（可选）
echo   set XM_LLM_BASE_URL="https://litellm-test.summerfarm.net/v1"
echo   set XM_LLM_MODEL="deepseek-v3-250324"
echo.
echo 要永久设置环境变量，请在系统环境变量中配置，或添加到用户环境变量中。
echo.
pause
