# 一键初始化鲜沐Claude Code

本文档提供了一键初始化鲜沐Claude Code的完整指南，帮助开发者快速配置Claude Code环境并集成鲜沐自定义命令，特别针对Java项目进行优化。

## 🚀 快速开始

### 1. 安装Claude Code和Claude Code Router

首先运行安装脚本：

```bash
# 克隆项目后进入项目目录
cd ai-code-review

# 运行安装脚本
./setup_claude_code.sh
```

该脚本将自动完成以下操作：
- 安装Claude Code (全局)
- 安装Claude Code Router (全局)
- 复制xm命令集到 `~/.claude/commands/xm`
- 配置Router配置文件到 `~/.claude-code-router/config.json`

### 2. 启动Claude Code服务

```bash
# 进入具体的项目空间目录，如item-center或summerfarm-mall
cd /path/to/your/project

# 启动Claude Code服务（会同时启动claude code router和claude code）
ccr start
```

## 🛠️ 鲜沐自定义命令介绍

项目包含以下鲜沐自定义命令，针对Java开发进行了优化：

### 1. `/xm:setup-java` - Java项目配置
快速设置Java项目配置，创建CLAUDE.md文件以帮助Claude Code理解项目架构。

使用方法：
```bash
/xm:setup-java
```

功能：
- 分析项目DDD架构
- 创建CLAUDE.md配置文件
- 配置适当的工具权限

### 2. `/xm:cr-java` - Java代码审查
对比当前分支和master/main分支进行代码审查。

使用方法：
```bash
/xm:cr-java
```

功能：
- 自动获取最新代码
- 进行全面代码评审
- 生成评审报告

### 3. `/xm:gitcommit` - Git提交
创建带有中文消息的Git提交并推送到远程。

使用方法：
```bash
/xm:gitcommit
```

功能：
- 自动生成中文提交消息
- 自动添加文件并提交
- 推送到远程仓库

### 4. `/xm:junit-java` - JUnit测试生成
快速启动JUnit5单元测试编写。

使用方法：
```bash
/xm:junit-java
```

功能：
- 分析现有代码
- 生成高质量JUnit5测试用例
- 提供测试运行命令

### 5. `/xm:junit5-migration` - JUnit迁移
将Java Spring Boot项目从JUnit4迁移到JUnit5。

使用方法：
```bash
/xm:junit5-migration
```

功能：
- 自动化替换测试代码
- 移除JUnit4依赖
- 确保Spring Boot兼容性

### 6. CRUD生成器
基于DDL语句自动生成CRUD代码。

使用方法：
```bash
/xm:crud_generator
```

功能：
- 智能项目架构检测
- 自适应代码生成
- 支持多种架构风格

## 📁 配置文件说明

### Claude Code Router配置
配置文件位于：`~/.claude-code-router/config.json`

主要配置项：
- 默认使用xm提供商的qwen3-coder模型
- 长上下文场景使用kimi-k2-turbo模型
- Web搜索使用openrouter的gemini-2.5-pro模型

### 自定义命令目录
命令文件位于：`~/.claude/commands/xm/`

包含以下命令文件：
- `setup-java.md` - Java项目配置命令
- `cr-java.md` - Java代码审查命令
- `gitcommit.md` - Git提交命令
- `junit-java.md` - JUnit测试生成命令
- `junit5-migration.md` - JUnit迁移命令
- `crud_generator.md` - CRUD生成器命令

## 🎯 Java项目初始化流程

### 1. 项目配置初始化
在新的Java项目中运行：
```bash
/xm:setup-java
```

这将：
- 分析项目结构和DDD架构
- 创建CLAUDE.md配置文件
- 配置适当的工具权限

### 2. 代码审查配置
在开发过程中定期运行：
```bash
/xm:cr-java
```

### 3. 单元测试配置
为现有代码生成测试：
```bash
/xm:junit-java
```

或者迁移JUnit4到JUnit5：
```bash
/xm:junit5-migration
```

## ⚡ 最佳实践

### 1. 日常开发流程
1. 使用`/xm:setup-java`初始化新项目
2. 开发过程中使用`/xm:cr-java`进行代码审查
3. 使用`/xm:junit-java`生成单元测试
4. 使用`/xm:gitcommit`提交代码

### 2. 项目迁移流程
1. 使用`/xm:junit5-migration`迁移测试框架
2. 使用`/xm:setup-java`重新配置项目
3. 使用`/xm:cr-java`验证迁移结果

### 3. 新功能开发流程
1. 使用CRUD生成器快速生成基础代码
2. 使用`/xm:junit-java`生成测试用例
3. 使用`/xm:cr-java`进行代码审查
4. 使用`/xm:gitcommit`提交代码

## 📋 常见问题解答

### Q: 安装失败怎么办？
A: 确保已安装Node.js和npm，然后重新运行安装脚本。

### Q: 命令无法使用怎么办？
A: 检查Claude Code Router是否正在运行，并确认命令文件已正确复制到`~/.claude/commands/xm/`目录。

### Q: 如何更新自定义命令？
A: 重新运行安装脚本，或者手动复制新的命令文件到`~/.claude/commands/xm/`目录。

## 🔄 更新和维护

### 更新Claude Code
```bash
npm update -g @anthropic-ai/claude-code
```

### 更新Claude Code Router
```bash
npm update -g @musistudio/claude-code-router
```

### 更新自定义命令
重新运行安装脚本：
```bash
./setup_claude_code.sh
```

## 📞 技术支持

如遇到问题，请联系技术支持团队或查看相关文档。