#!/bin/bash

# Claude Code 安装和配置脚本
# 用于安装 Claude Code 和 Claude Code Router，并配置相关文件

set -e  # 遇到错误时退出

echo "🚀 开始安装 Claude Code 和 Claude Code Router..."

# 检查 Node.js 和 npm 是否已安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm 未安装，请先安装 Node.js 和 npm"
    exit 1
fi

echo "📦 正在安装 Claude Code..."
# 安装 Claude Code
npm install -g @anthropic-ai/claude-code --registry=https://registry.npmmirror.com

if [ $? -eq 0 ]; then
    echo "✅ Claude Code 安装成功"
else
    echo "❌ Claude Code 安装失败"
    exit 1
fi

echo "📦 正在安装 Claude Code Router..."
# 安装 Claude Code Router
npm install -g @musistudio/claude-code-router --registry=https://registry.npmmirror.com

if [ $? -eq 0 ]; then
    echo "✅ Claude Code Router 安装成功"
else
    echo "❌ Claude Code Router 安装失败"
    exit 1
fi

echo "📁 正在配置 Claude Code 命令..."

# 创建 ~/.claude/commands 目录
CLAUDE_COMMANDS_DIR="$HOME/.claude/commands"
mkdir -p "$CLAUDE_COMMANDS_DIR"

# 复制 xm 文件夹到用户的 ~/.claude/commands 目录
if [ -d "claude/commands/xm" ]; then
    cp -r "claude/commands/xm" "$CLAUDE_COMMANDS_DIR/"
    echo "✅ 已复制 xm 命令到 $CLAUDE_COMMANDS_DIR/xm"
else
    echo "⚠️  警告: claude/commands/xm 目录不存在，跳过复制"
fi

echo "📁 正在配置 Claude Code Router..."

# 创建 ~/.claude-code-router 目录
CLAUDE_ROUTER_DIR="$HOME/.claude-code-router"
mkdir -p "$CLAUDE_ROUTER_DIR"

# 复制 config.json 到 ~/.claude-code-router/config.json
if [ -f "claude/claude-code-router/config.json" ]; then
    cp "claude/claude-code-router/config.json" "$CLAUDE_ROUTER_DIR/config.json"
    echo "✅ 已复制配置文件到 $CLAUDE_ROUTER_DIR/config.json"
else
    echo "⚠️  警告: claude/claude-code-router/config.json 文件不存在，跳过复制"
fi

echo ""
echo "🎉 Claude Code 和 Claude Code Router 安装配置完成！"
echo ""
echo "📋 安装的组件："
echo "   • Claude Code (全局安装)"
echo "   • Claude Code Router (全局安装)"
echo "   • xm 命令集 (位于 ~/.claude/commands/xm)"
echo "   • Router 配置文件 (位于 ~/.claude-code-router/config.json)"
echo ""
echo "🔧 使用方法："
echo "   • 运行 'claude-code' 启动 Claude Code"
echo "   • 运行 'claude-code-router' 启动 Claude Code Router"
echo ""
echo "💡 提示："
echo "   • 如需修改配置，请编辑 ~/.claude-code-router/config.json"
echo "   • 如需添加更多命令，请在 ~/.claude/commands 目录下添加"
echo ""
