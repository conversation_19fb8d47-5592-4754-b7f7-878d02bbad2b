# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains a Merge Request Code Review Tool that automatically fetches code diffs from Alibaba Cloud CodeUp URLs and performs AI-powered code reviews. The tool can automatically post comments to the CodeUp platform.

## Key Components

1. **Main Application**: `mr_code_review.py` - The core Python script that handles URL parsing, API calls, diff retrieval, and AI review
2. **Installation Scripts**: `setup.sh`, `setup.bat`, `setup.ps1` - Cross-platform installation scripts
3. **Dependencies**: `requirements.txt` - Python dependencies including Alibaba Cloud SDK and OpenAI SDK
4. **System Instructions**: `code_review_instruction.txt` - AI review guidelines and prompts
5. **Claude Commands**: Custom commands in `claude/commands/xm/` for code review and git commit automation

## Common Development Tasks

### Running the Code Review Tool

Interactive mode:
```bash
python mr_code_review.py
```

Command-line mode:
```bash
python mr_code_review.py --url "https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"
```

### Installation

Automatic installation (recommended):
```bash
# Linux/macOS
./setup.sh

# Windows (Command Prompt)
setup.bat

# Windows (PowerShell)
.\setup.ps1
```

Manual installation:
```bash
pip install -r requirements.txt
```

### Environment Configuration

Required environment variables:
```bash
# Alibaba Cloud credentials
export ALIBABA_CLOUD_ACCESS_KEY_ID="your-access-key-id"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your-access-key-secret"

# LLM API key
export XM_LLM_API_KEY="your-api-key"
```

Optional environment variables:
```bash
# LLM configuration
export XM_LLM_BASE_URL="https://litellm-test.summerfarm.net/v1"
export XM_LLM_MODEL="deepseek-v3-250324"

# Comment control
export XM_NEED_COMMENT="true"

# File exclusion patterns
export MR_REVIEW_EXCLUDE_PATTERNS="test/java/,*Test.java,*.md,*.txt,*.json"
```

## Code Architecture

The application follows a modular architecture with these main components:

1. **URLParser**: Parses CodeUp URLs to extract organization, project, and merge request information
2. **DevOpsClient**: Wrapper around Alibaba Cloud DevOps SDK for API interactions
3. **AICodeReviewer**: Handles AI code review using LLM APIs with streaming responses
4. **Comment System**: Posts inline and global comments to CodeUp merge requests

## Development Guidelines

- Follow Python best practices and PEP 8 style guide
- Handle errors gracefully with proper exception handling
- Use environment variables for configuration
- Maintain clear separation of concerns between components
- Write clear, descriptive comments in Chinese
- Test changes with real CodeUp merge request URLs

## Testing

The tool is tested by running it against actual CodeUp merge requests. Ensure you have valid credentials and permissions before testing.

## Customization

- Modify `code_review_instruction.txt` to customize AI review behavior
- Adjust exclusion patterns via `MR_REVIEW_EXCLUDE_PATTERNS` environment variable
- Change LLM model via `XM_LLM_MODEL` environment variable